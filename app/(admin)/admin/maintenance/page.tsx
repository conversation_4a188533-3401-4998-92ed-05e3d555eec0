"use client";

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  Wrench,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Car,
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function MaintenancePage() {
  // Mock data - replace with actual API call
  const maintenanceSchedules = [
    {
      id: 1,
      vehicle: "Toyota Fortuner",
      group: "Family SUV",
      type: "Regular Service",
      scheduledDate: "2024-02-20",
      status: "Scheduled",
      provider: "QuickFix Mechanics",
      estimatedCost: 1500,
      notes: "60,000 km service",
    },
    {
      id: 2,
      vehicle: "VW Polo",
      group: "Weekend Getaway",
      type: "Tire Replacement",
      scheduledDate: "2024-02-15",
      status: "Completed",
      provider: "AutoCare Specialists",
      estimatedCost: 3200,
      notes: "All four tires replaced",
    },
    {
      id: 3,
      vehicle: "Honda Civic",
      group: "Work Commute",
      type: "Brake Inspection",
      scheduledDate: "2024-02-25",
      status: "Overdue",
      provider: "QuickFix Mechanics",
      estimatedCost: 800,
      notes: "Brake pads may need replacement",
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          Maintenance Schedules
        </h1>
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search maintenance..."
              className="pl-10 w-[300px]"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Schedule Maintenance
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Total Scheduled</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-[#009639]" />
              <span className="text-2xl font-bold">15</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="text-2xl font-bold text-green-600">8</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Upcoming (7 days)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Wrench className="h-5 w-5 text-blue-600" />
              <span className="text-2xl font-bold text-blue-600">5</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Overdue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <span className="text-2xl font-bold text-red-600">2</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Maintenance Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Vehicle</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Scheduled Date</TableHead>
                <TableHead>Provider</TableHead>
                <TableHead>Est. Cost</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {maintenanceSchedules.map((schedule) => (
                <TableRow key={schedule.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Car className="h-4 w-4 text-[#009639]" />
                      <Link
                        href={`/admin/vehicles/${schedule.id}`}
                        className="text-[#009639] hover:text-[#007A2F]"
                      >
                        {schedule.vehicle}
                      </Link>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/groups/${schedule.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {schedule.group}
                    </Link>
                  </TableCell>
                  <TableCell>{schedule.type}</TableCell>
                  <TableCell>{schedule.scheduledDate}</TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/partners/${schedule.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {schedule.provider}
                    </Link>
                  </TableCell>
                  <TableCell>
                    R {schedule.estimatedCost.toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        schedule.status === "Completed"
                          ? "bg-green-100 text-green-800"
                          : schedule.status === "Scheduled"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {schedule.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/maintenance/${schedule.id}`}
                      className="text-[#009639] hover:text-[#007A2F] font-medium"
                    >
                      View Details
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
