"use client";

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Filter, Download, Upload } from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function CompliancePage() {
  // Mock data - replace with actual API call
  const submissions = [
    {
      id: 1,
      groupName: "Family Car Share",
      submissionDate: "2024-01-15",
      status: "Pending Review",
      documents: [
        { name: "Registration Documents", status: "Submitted" },
        { name: "ID Verification", status: "Submitted" },
        { name: "Proof of Address", status: "Pending" },
      ],
    },
    {
      id: 2,
      groupName: "Business Fleet Group",
      submissionDate: "2024-01-16",
      status: "Approved",
      documents: [
        { name: "Registration Documents", status: "Approved" },
        { name: "ID Verification", status: "Approved" },
        { name: "Proof of Address", status: "Approved" },
      ],
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          Compliance Management
        </h1>
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search submissions..."
              className="pl-10 w-[300px]"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {submissions.map((submission) => (
          <Card key={submission.id}>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>{submission.groupName}</CardTitle>
                <p className="text-sm text-gray-500">
                  Submitted on {submission.submissionDate}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <span
                  className={`px-3 py-1 rounded-full text-sm ${
                    submission.status === "Approved"
                      ? "bg-green-100 text-green-800"
                      : "bg-yellow-100 text-yellow-800"
                  }`}
                >
                  {submission.status}
                </span>
                <Link
                  href={`/admin/compliance/${submission.id}`}
                  className="text-[#009639] hover:text-[#007A2F] font-medium"
                >
                  View Details
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Document</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {submission.documents.map((doc, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">
                          {doc.name}
                        </TableCell>
                        <TableCell>
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${
                              doc.status === "Approved"
                                ? "bg-green-100 text-green-800"
                                : doc.status === "Submitted"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {doc.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-600 hover:text-gray-800"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            {doc.status !== "Approved" && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-[#009639] hover:text-[#007A2F]"
                              >
                                <Upload className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {submission.status === "Pending Review" && (
                  <div className="flex justify-end gap-2 mt-4">
                    <Button variant="outline" className="text-red-600">
                      Reject
                    </Button>
                    <Button className="bg-[#009639] hover:bg-[#007A2F] text-white">
                      Approve
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
