"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  Users,
  BarChart,
  FileText,
  Settings,
  HelpCircle,
  Search,
  Bell,
  MessageSquare,
  Activity,
  History,
  CheckSquare,
  Handshake,
  LineChart,
} from "lucide-react";
import Image from "next/image";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  const isActive = (path: string) => {
    return pathname === path;
  };

  return (
    <div className="min-h-screen bg-[#f8f9fa] flex">
      {/* Sidebar */}
      <div className="w-64 bg-[#009639] text-white flex flex-col h-screen fixed">
        <div className="p-4 flex items-center">
          <div className="bg-white rounded-md w-8 h-8 flex items-center justify-center mr-2">
            <span className="text-[#009639] font-bold">P</span>
          </div>
          <h1 className="text-xl font-bold">Poolly Admin</h1>
        </div>

        <div className="mt-6 px-4">
          <div className="text-sm font-medium text-[#e6ffe6] mb-2">MAIN</div>
          <nav className="space-y-1">
            <Link
              href="/admin/dashboard"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/dashboard")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <LayoutDashboard size={18} className="mr-3" />
              <span>Dashboard</span>
            </Link>
            <Link
              href="/admin/users"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/users")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <Users size={18} className="mr-3" />
              <span>Users</span>
            </Link>
            <Link
              href="/admin/groups"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/groups")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <Users size={18} className="mr-3" />
              <span>Groups</span>
            </Link>
            <Link
              href="/admin/bookings"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/bookings")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <FileText size={18} className="mr-3" />
              <span>Bookings</span>
            </Link>
          </nav>
        </div>

        <div className="mt-6 px-4">
          <div className="text-sm font-medium text-[#e6ffe6] mb-2">
            MANAGEMENT
          </div>
          <nav className="space-y-1">
            <Link
              href="/admin/vehicles"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/vehicles")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <FileText size={18} className="mr-3" />
              <span>Vehicles</span>
            </Link>
            <Link
              href="/admin/maintenance"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/maintenance")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <Settings size={18} className="mr-3" />
              <span>Maintenance</span>
            </Link>
            <Link
              href="/admin/handovers"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/handovers")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <Users size={18} className="mr-3" />
              <span>Handovers</span>
            </Link>
            <Link
              href="/admin/compliance"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/compliance")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <FileText size={18} className="mr-3" />
              <span>Compliance</span>
            </Link>
          </nav>
        </div>

        <div className="mt-6 px-4">
          <div className="text-sm font-medium text-[#e6ffe6] mb-2">
            MONITORING
          </div>
          <nav className="space-y-1">
            <Link
              href="/admin/transactions"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/transactions")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <BarChart size={18} className="mr-3" />
              <span>Transactions</span>
            </Link>
            <Link
              href="/admin/approvals"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/approvals")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <CheckSquare size={18} className="mr-3" />
              <span>Approvals</span>
            </Link>
            <Link
              href="/admin/partners"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/partners")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <Handshake size={18} className="mr-3" />
              <span>Partners</span>
            </Link>
          </nav>
        </div>

        <div className="mt-6 px-4">
          <div className="text-sm font-medium text-[#e6ffe6] mb-2">
            INSIGHTS
          </div>
          <nav className="space-y-1">
            <Link
              href="/admin/analytics"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/analytics")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <LineChart size={18} className="mr-3" />
              <span>Analytics</span>
            </Link>
          </nav>
        </div>

        <div className="mt-6 px-4">
          <div className="text-sm font-medium text-[#e6ffe6] mb-2">SYSTEM</div>
          <nav className="space-y-1">
            <Link
              href="/admin/settings"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/settings")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <Settings size={18} className="mr-3" />
              <span>Settings</span>
            </Link>
            <Link
              href="/admin/help"
              className={`flex items-center px-4 py-2 rounded-md ${
                isActive("/admin/help")
                  ? "bg-[#007A2F] text-white"
                  : "text-[#e6ffe6] hover:bg-[#007A2F]"
              }`}
            >
              <HelpCircle size={18} className="mr-3" />
              <span>Help</span>
            </Link>
          </nav>
        </div>

        <div className="mt-auto p-4 border-t border-[#007A2F]">
          <Link href="/profile">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-2">
                <span className="text-[#009639] font-medium">A</span>
              </div>
              <div>
                <p className="text-sm font-medium">Admin User</p>
                <p className="text-xs text-[#e6ffe6]"><EMAIL></p>
              </div>
            </div>
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="ml-64 flex-1">
        {/* Top Navigation */}
        <div className="bg-white h-16 border-b border-gray-200 flex items-center justify-between px-6">
          <div className="flex items-center">
            <div className="relative">
              <Search
                size={18}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <input
                type="text"
                placeholder="Search..."
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              />
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <button className="relative p-2 text-gray-500 hover:text-gray-700">
              <Bell size={20} />
              <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>
            <button className="relative p-2 text-gray-500 hover:text-gray-700">
              <MessageSquare size={20} />
            </button>
            <div className="flex items-center">
              <Link href="/profile">
                <div className="w-8 h-8 rounded-full bg-[#009639] flex items-center justify-center text-white">
                  <span className="font-medium">A</span>
                </div>
              </Link>
            </div>
          </div>
        </div>

        {/* Page Content */}
        <div className="p-6">{children}</div>
      </div>
    </div>
  );
}
