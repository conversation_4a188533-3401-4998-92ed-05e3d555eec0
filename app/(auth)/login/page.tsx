"use client";

import { signin } from "@/actions/auth";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { setCredentials } from "@/lib/features/auth/authSlice";
import { useAppDispatch } from "@/lib/hooks";
import { Eye, EyeOff, ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

// Define the validation schema with Zod
const loginSchema = z.object({
  emailOrPhone: z
    .string()
    .min(1, "Emailis required")
    .refine(
      (value) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(value);
      },
      {
        message: "Please enter a valid email",
      }
    ),
  password: z.string().min(6, "Password must be at least 6 characters"),
  rememberMe: z.boolean().optional(),
});

// Infer the form data type from the schema
type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginScreen() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  // Initialize react-hook-form with zod resolver
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      emailOrPhone: "",
      password: "",
      rememberMe: false,
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    setError("");

    try {
      const nextStep = await signin({
        email: data.emailOrPhone,
        password: data.password,
      });
      console.log("nextStep", nextStep);

      dispatch(setCredentials({ email: data.emailOrPhone }));

      if (nextStep.signInStep === "DONE") {
        router.push("/home");
      } else if (nextStep.signInStep === "CONFIRM_SIGN_UP") {
        router.push(`/verification?deliveryMedium`);
      } else if (nextStep.signInStep === "RESET_PASSWORD") {
        router.push(`/reset-password`);
      }
    } catch (err: any) {
      if (err.name === "UserAlreadyAuthenticatedException") {
        router.push("/home");
      }
      if (err && err.message) {
        setError(err.message);
      } else {
        setError("An error occurred. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white px-6 py-8">
      {/* Header */}
      <div className="flex items-center mb-8">
        <Button
          variant="ghost"
          className="bg-[#e6ffe6] rounded-full p-2 mr-4 shadow-sm"
          onClick={() => router.push("/welcome")}
        >
          <ArrowLeft size={24} className="text-[#009639]" />
        </Button>
        <h1 className="text-2xl font-bold text-[#333333]">Log In</h1>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <Label
            htmlFor="emailOrPhone"
            className="block text-sm font-medium text-[#333333] mb-1"
          >
            Email
          </Label>
          <Input
            type="text"
            id="emailOrPhone"
            {...register("emailOrPhone")}
            className="w-full p-3 border border-[#e6e6e6] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
            placeholder="Enter your email"
          />
          {errors.emailOrPhone && (
            <p className="mt-1 text-sm text-red-600">
              {errors.emailOrPhone.message}
            </p>
          )}
        </div>

        <div>
          <Label
            htmlFor="password"
            className="block text-sm font-medium text-[#333333] mb-1"
          >
            Password
          </Label>
          <div className="relative">
            <Input
              type={showPassword ? "text" : "password"}
              id="password"
              {...register("password")}
              className="w-full p-3 border border-[#e6e6e6] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              placeholder="Enter your password"
            />
            <Button
              type="button"
              variant="ghost"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#797879]"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </Button>
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-red-600">
              {errors.password.message}
            </p>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="rememberMe"
              {...register("rememberMe")}
              className="h-4 w-4 text-[#009639] focus:ring-[#009639] border-gray-300 rounded"
            />
            <Label
              htmlFor="rememberMe"
              className="ml-2 block text-sm text-[#797879]"
            >
              Remember me
            </Label>
          </div>
          <Button
            type="button"
            variant="link"
            className="text-sm text-[#009639] font-medium"
            onClick={() => router.push("/forgot-password")}
          >
            Forgot password?
          </Button>
        </div>

        {error && <p className="text-sm text-red-600">{error}</p>}

        <Button
          type="submit"
          disabled={isLoading}
          className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-3 rounded-full font-semibold shadow-md"
        >
          {isLoading ? "Logging in..." : "Log In"}
        </Button>
      </form>

      {/* Sign up link */}
      <div className="mt-8 text-center">
        <p className="text-[#797879]">
          Don't have an account?{" "}
          <Button
            variant="link"
            className="text-[#009639] font-medium"
            onClick={() => router.push("/signup")}
          >
            Sign Up
          </Button>
        </p>
      </div>
    </div>
  );
}
