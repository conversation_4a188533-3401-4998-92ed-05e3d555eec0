"use client";

import { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ArrowLeft,
  Car,
  Calendar,
  FileText,
  Users,
  Upload,
  X,
} from "lucide-react";
import {
  DisputeCreate,
  DisputeType,
  DisputeStatus,
  Priority,
} from "@/types/disputes";
import type { CompanyMembershipRead } from "@/types/company-ownerships";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createDisputeWithMedial } from "@/actions/dispute";
import { Textarea } from "@/components/ui/textarea";
import { DocumentDelete, DocumentUpload } from "@/lib/utils";

// Zod schema for form validation
const disputeSchema = z.object({
  name: z.string().min(1, "Dispute name is required"),
  description: z.string().min(1, "Description is required"),
  vehicle_id: z.number().min(1, "Please select a vehicle"),
  dispute_type: z.enum([
    DisputeType.VEHICLE_DAMAGE,
    DisputeType.BOOKING,
    DisputeType.MAINTENANCE_COST_DISPUTE,
    DisputeType.VEHICLE_MAINTENANCE,
    DisputeType.OTHER,
  ]),
  party_offending: z.number().min(1, "Please select an offending party"),
  party_logging: z.number().min(1, "Party logging is required"),
  dispute_status: z.enum([
    DisputeStatus.OPEN,
    DisputeStatus.DELETED,
    DisputeStatus.RESOLVED,
  ]),
  priority: z.enum([Priority.HIGH, Priority.MEDIUM, Priority.LOW]),
  company_id: z.number().min(1, "Please select a company"),
});

interface ReportIssueScreenProps {
  ownershipData: CompanyMembershipRead;
  loggedInUserPartyId: number;
  vehicles: VehicleReadWithModelAndParty[];
}
interface UploadResult {
  path: string;
}
export default function ReportIssueScreen({
  ownershipData,
  loggedInUserPartyId,
  vehicles,
}: ReportIssueScreenProps) {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [selectedVehicle, setSelectedVehicle] = useState<number | null>(null);
  const [submitting, setSubmitting] = useState(false); // Track submission state
  const [documentUrls, setDocumentUrls] = useState<string[]>([]);
  const [documents, setDocuments] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const form = useForm<DisputeCreate>({
    resolver: zodResolver(disputeSchema),
    defaultValues: {
      name: "",
      description: "",
      vehicle_id: 0,
      dispute_type: DisputeType.OTHER,
      party_offending: 0,
      party_logging: loggedInUserPartyId,
      dispute_status: DisputeStatus.OPEN,
      priority: Priority.LOW,
      company_id: 0,
    },
  });

  const disputeTypes = [
    {
      id: DisputeType.VEHICLE_DAMAGE,
      label: "Vehicle Damage",
      icon: <Car size={16} />,
    },
    { id: DisputeType.BOOKING, label: "Booking", icon: <Calendar size={16} /> },
    {
      id: DisputeType.MAINTENANCE_COST_DISPUTE,
      label: "Cost Dispute",
      icon: <FileText size={16} />,
    },
    {
      id: DisputeType.VEHICLE_MAINTENANCE,
      label: "Vehicle Maintenance",
      icon: <Users size={16} />,
    },
    { id: DisputeType.OTHER, label: "Other", icon: <Users size={16} /> },
  ];

  const priorities = [
    {
      id: Priority.HIGH,
      label: "High Priority",
      color: "bg-[#ffe6e6] text-[#7A0000]",
    },
    {
      id: Priority.MEDIUM,
      label: "Medium Priority",
      color: "bg-[#fff8e6] text-[#7A6500]",
    },
    {
      id: Priority.LOW,
      label: "Low Priority",
      color: "bg-[#e6ffe6] text-[#007A2F]",
    },
  ];

  const onSubmit = async (data: DisputeCreate) => {
    setSubmitting(true);
    setError(null);
    try {
      await createDisputeWithMedial({ ...data, media: documentUrls });
      router.push("/dispute-resolution");
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "Failed to submit dispute. Please try again."
      );
    } finally {
      setSubmitting(false);
    }
  };
  const handleDocumentUpload = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      setError("No files selected.");
      return;
    }

    try {
      const uploadPromises = Array.from(files).map((file) =>
        DocumentUpload(file, "vehicle")
      );
      const uploadResults = (await Promise.all(
        uploadPromises
      )) as UploadResult[];

      const newUrls = uploadResults
        .filter((result) => result && result.path)
        .map((result) => result.path);

      if (newUrls.length > 0) {
        // Append new URLs to existing ones
        setDocumentUrls((prevUrls) => [...prevUrls, ...newUrls]);
        // Append new files to existing ones
        setDocuments((prevDocs) => [...prevDocs, ...Array.from(files)]);
      } else {
        setError("File upload failed. No valid paths returned.");
      }
    } catch (err: any) {
      console.error("Upload error:", err);
      setError(
        err.message || "An error occurred during file upload. Please try again."
      );
    }
  };

  async function handleRemoveDocument(index: number, path: string) {
    if (index < 0 || index >= documents.length) {
      setError("Invalid document index.");
      console.error("Invalid index:", index);
      return false;
    }

    try {
      // Delete the file from storage (e.g., S3)
      await DocumentDelete(path);

      // Update both documents and documentUrls states
      setDocuments((prev) => prev.filter((_, i) => i !== index));
      setDocumentUrls((prev) => prev.filter((_, i) => i !== index));

      // Clear any previous error
      setError(null);
      return true; // Indicate success
    } catch (error: any) {
      console.error("Error deleting file from S3:", error);
      setError(error.message || "Failed to delete the file. Please try again.");
      return false; // Indicate failure
    }
  }
  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </Button>
          <h1 className="text-xl font-bold text-white">Report an Issue</h1>
        </div>
      </div>

      {/* Form */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">
            Create New Dispute
          </h3>
          {error && (
            <div className="mb-4 p-2 bg-[#ffe6e6] rounded-lg shadow-sm">
              <p className="text-xs text-[#7A0000]">{error}</p>
            </div>
          )}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {/* Dispute Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-[#333333] font-medium">
                      Dispute Name
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter dispute name"
                        className="w-full mt-1 p-2 border border-gray-200 rounded-lg text-sm text-[#333333] focus:ring-[#009639] focus:border-[#009639]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-[#333333] font-medium">
                      Description
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        value={field.value ?? ""}
                        placeholder="Describe the issue"
                        className="w-full mt-1 p-2 border border-gray-200 rounded-lg text-sm text-[#333333] focus:ring-[#009639] focus:border-[#009639]"
                        rows={4}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Dispute Type */}
              <FormField
                control={form.control}
                name="dispute_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-[#333333] font-medium">
                      Dispute Type
                    </FormLabel>
                    <FormControl>
                      <div className="flex space-x-2 mt-1 overflow-x-auto">
                        {disputeTypes.map((type) => (
                          <Button
                            key={type.id}
                            type="button"
                            variant={
                              field.value === type.id ? "default" : "secondary"
                            }
                            className={`flex items-center px-3 py-1 rounded-full text-sm whitespace-nowrap ${
                              field.value === type.id
                                ? "bg-[#009639] text-white"
                                : "bg-[#f2f2f2] text-[#333333]"
                            }`}
                            onClick={() => field.onChange(type.id)}
                          >
                            {type.icon}
                            <span className="ml-1">{type.label}</span>
                          </Button>
                        ))}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Priority */}
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-[#333333] font-medium">
                      Priority
                    </FormLabel>
                    <FormControl>
                      <div className="flex space-x-2 mt-1">
                        {priorities.map((priority) => (
                          <Button
                            key={priority.id}
                            type="button"
                            className={`px-3 py-1 rounded-full text-sm ${priority.color} ${
                              field.value === priority.id ? "font-semibold" : ""
                            }`}
                            onClick={() => field.onChange(priority.id)}
                          >
                            {priority.label}
                          </Button>
                        ))}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Vehicle ID */}
              <FormField
                control={form.control}
                name="vehicle_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-[#333333] font-medium">
                      Vehicle
                    </FormLabel>
                    <FormControl>
                      <div className="flex items-center space-x-4">
                        <Select
                          value={field.value ? field.value.toString() : ""}
                          onValueChange={(value) => {
                            const val = parseInt(value) || 0;
                            field.onChange(val);
                            setSelectedVehicle(val || null);
                          }}
                        >
                          <SelectTrigger className="w-full mt-1 p-2 border border-gray-200 rounded-lg text-sm text-[#333333] focus:ring-[#009639] focus:border-[#009639]">
                            <SelectValue placeholder="Select Vehicle" />
                          </SelectTrigger>
                          <SelectContent>
                            {vehicles.map((vehicle) => (
                              <SelectItem
                                key={vehicle.id}
                                value={vehicle.id.toString()}
                              >
                                {vehicle.model?.model}{" "}
                                {vehicle.model?.make?.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {selectedVehicle && (
                          <Button
                            type="button"
                            className="bg-[#009639] text-white py-2 px-4 rounded-full text-sm flex items-center justify-center shadow-sm"
                            onClick={() =>
                              router.push(
                                `/schedule-maintenance/${selectedVehicle}`
                              )
                            }
                          >
                            Schedule
                          </Button>
                        )}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Party Logging (Read-only) */}
              <FormField
                control={form.control}
                name="party_logging"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-[#333333] font-medium">
                      Party Logging
                    </FormLabel>
                    <FormControl>
                      <Input
                        value={
                          ownershipData.individuals.find(
                            (ind) => ind.party_id === field.value
                          )
                            ? `${ownershipData.individuals.find((ind) => ind.party_id === field.value)!.first_name} ${
                                ownershipData.individuals.find(
                                  (ind) => ind.party_id === field.value
                                )!.last_name
                              } (You)`
                            : "You"
                        }
                        disabled
                        className="w-full mt-1 p-2 border border-gray-200 rounded-lg text-sm text-[#333333] bg-gray-100"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Party Offending */}
              <FormField
                control={form.control}
                name="party_offending"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-[#333333] font-medium">
                      Party Offending
                    </FormLabel>
                    <FormControl>
                      <Select
                        value={field.value ? field.value.toString() : ""}
                        onValueChange={(value) =>
                          field.onChange(parseInt(value) || 0)
                        }
                      >
                        <SelectTrigger className="w-full mt-1 p-2 border border-gray-200 rounded-lg text-sm text-[#333333] focus:ring-[#009639] focus:border-[#009639]">
                          <SelectValue placeholder="Select offending party" />
                        </SelectTrigger>
                        <SelectContent>
                          {ownershipData.individuals.map((individual) => (
                            <SelectItem
                              key={individual.party_id}
                              value={individual.party_id.toString()}
                            >
                              {individual.first_name} {individual.last_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Company ID */}
              <FormField
                control={form.control}
                name="company_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-[#333333] font-medium">
                      Company
                    </FormLabel>
                    <FormControl>
                      <Select
                        value={field.value ? field.value.toString() : ""}
                        onValueChange={(value) =>
                          field.onChange(parseInt(value) || 0)
                        }
                      >
                        <SelectTrigger className="w-full mt-1 p-2 border border-gray-200 rounded-lg text-sm text-[#333333] focus:ring-[#009639] focus:border-[#009639]">
                          <SelectValue placeholder="Select company" />
                        </SelectTrigger>
                        <SelectContent>
                          {ownershipData.companies.map((company) => (
                            <SelectItem
                              key={company.id}
                              value={company.id.toString()}
                            >
                              {company.alias}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-[#333333] font-medium">Images</h3>
                  <input
                    multiple
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    onChange={handleDocumentUpload}
                  />
                </div>

                {documents.length > 0 ? (
                  <ul className="space-y-4">
                    {documents.map((file, index) => (
                      <li
                        key={index}
                        className="bg-[#f9f9f9] p-4 rounded-xl border border-[#e5e7eb] shadow-sm"
                      >
                        <div className="flex items-start justify-between gap-4 mb-3">
                          {/* File icon + file name (take more space) */}
                          <div className="flex flex-1 items-center gap-2 min-w-0">
                            <FileText
                              size={20}
                              className="text-[#009639] shrink-0"
                            />
                            <span className="text-sm text-[#333333] font-medium break-all truncate">
                              {file.name}
                            </span>
                          </div>
                          <div className="flex items-start justify-between gap-4 mb-3"></div>
                          {/* Delete button */}
                          <button
                            type="button"
                            className="hover:bg-red-50 rounded-full p-1 transition shrink-0"
                            onClick={async () => {
                              await handleRemoveDocument(
                                index,
                                documentUrls[index]
                              );
                            }}
                          >
                            <X size={14} className="text-red-500" />
                          </button>
                        </div>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div
                    className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload size={32} className="text-[#009639] mb-2" />
                    <p className="text-[#797879] text-center">
                      Upload your vehicle images here
                    </p>
                  </div>
                )}
              </div>
              {submitting ? (
                <Button
                  disabled
                  type="submit"
                  className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-4 rounded-full text-xl font-semibold mt-8 shadow-md flex justify-center items-center"
                >
                  <div className="border-t-4 border-b-4 border-white w-6 h-6 border-solid rounded-full animate-spin"></div>
                </Button>
              ) : (
                <Button
                  type="submit"
                  className="w-full py-3 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white rounded-full font-semibold shadow-md"
                >
                  Submit Dispute
                </Button>
              )}
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
