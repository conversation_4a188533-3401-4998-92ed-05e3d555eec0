import BackElement from "@/app/components/back-element";
import VehicleDashboardTabs from "./vehicle-tabs";
import MissingParty from "@/components/missing-party";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import { getCompanyOwnershipByParty } from "@/actions/company-ownership";
import { getVehiclesByParties } from "@/actions/vehicles";

export default async function VehicleDashboardScreen() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId, email } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;
  const ownerships = await getCompanyOwnershipByParty(+dbId);
  const partyIds: number[] =
    ownerships.length > 0
      ? ownerships
          .map((ownership) => ownership.company?.party_id)
          .filter((id): id is number => typeof id === "number")
      : [0];

  const vehicles = await getVehiclesByParties(partyIds);
  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      <BackElement title="Vehicle Dashboard" />
      <VehicleDashboardTabs vehicles={vehicles} ownerships={ownerships} />
    </div>
  );
}
