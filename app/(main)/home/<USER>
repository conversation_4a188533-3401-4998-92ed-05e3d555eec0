"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  Bell,
  Search,
  ChevronRight,
  Settings,
  Wallet,
  Clock,
  Car,
  Users,
  Activity,
} from "lucide-react";
import { IndividualRead } from "@/types/individuals";

export default function HomeScreen({
  individual,
  profilePic,
}: {
  individual: IndividualRead;
  profilePic: string | null;
}) {
  const router = useRouter();
  // State for notifications
  const [showNotifications, setShowNotifications] = useState(false);

  const upcomingEvents = [
    {
      id: 1,
      title: "Vehicle Handover",
      description: "Toyota Hilux to Sarah",
      time: "Today, 3:00 PM",
      icon: <Car size={20} className="text-[#009639]" />,
    },
    {
      id: 2,
      title: "Maintenance Due",
      description: "Volkswagen Polo oil change",
      time: "Tomorrow, 10:00 AM",
      icon: <Clock size={20} className="text-[#009639]" />,
    },
    {
      id: 3,
      title: "Payment Due",
      description: "Monthly contribution",
      time: "In 3 days",
      icon: <Wallet size={20} className="text-[#009639]" />,
    },
  ];

  const recentActivities = [
    {
      id: 1,
      type: "booking",
      title: "You booked Toyota Hilux",
      time: "2 hours ago",
      image: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 2,
      type: "payment",
      title: "Monthly payment processed",
      time: "Yesterday",
      image: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 3,
      type: "group",
      title: "John joined Family SUV group",
      time: "2 days ago",
      image: "/placeholder.svg?height=40&width=40",
    },
  ];

  const opportunities = [
    {
      id: 1,
      title: "Weekend Getaway Car",
      description: "Join a group of 4 to co-own a weekend car",
      image: "/placeholder.svg?height=120&width=200",
    },
    {
      id: 2,
      title: "City Commuter EV",
      description: "Electric vehicle for daily city commutes",
      image: "/placeholder.svg?height=120&width=200",
    },
  ];

  // Removed quickActions array as we're using inline components

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#ffffff"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex justify-between items-center border-b border-[#007A2F]">
        <div className="flex items-center">
          <div className="w-10 h-10 ride-avatar mr-3">
            <Image
              src={profilePic || "/placeholder.svg?height=40&width=40"}
              alt="Profile"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
          <div>
            <p className="text-white text-sm">Hi there,</p>
            <h2 className="text-white font-bold">
              {individual.first_name} {individual.last_name}
            </h2>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            className="bg-[#007A2F] p-2 rounded-full shadow-sm"
            onClick={() => console.log("Search clicked")}
          >
            <Search size={18} className="text-white" />
          </button>
          <button
            className="bg-[#007A2F] p-2 rounded-full shadow-sm relative"
            onClick={() => setShowNotifications(!showNotifications)}
          >
            <Bell size={18} className="text-white" />
            <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          <button
            className="bg-[#007A2F] p-2 rounded-full shadow-sm"
            onClick={() => router.push("/profile")}
          >
            <Settings size={18} className="text-white" />
          </button>
        </div>
      </div>

      {/* Promotional Card */}
      <div className="mx-4 my-4">
        <div className="relative rounded-2xl p-0 overflow-hidden h-72 shadow-lg">
          {/* Background with gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#00b347] via-[#009639] to-[#007A2F] opacity-90"></div>
          {/* Glass overlay */}
          <div className="absolute inset-0 bg-black/5 backdrop-filter backdrop-blur-[1px]"></div>
          {/* Content container */}
          <div className="absolute inset-0 p-6 border border-white/20 rounded-2xl">
            <div className="relative z-10">
              <h3 className="text-white font-semibold mb-1">
                Welcome to Poolly!
              </h3>
              <p className="text-white text-2xl font-bold mb-4">
                Manage your shared vehicles with ease
              </p>
              <p className="text-white/80 mb-6 max-w-[70%]">
                Track usage, schedule maintenance, and monitor your vehicle's
                status all in one place.
              </p>
              <button
                className="bg-[#FFD700] text-[#333333] font-medium px-6 py-3 rounded-full text-sm shadow-md hover:bg-[#e6c200] transition-colors"
                onClick={() => router.push("/vehicle-dashboard")}
              >
                My Vehicles
              </button>
            </div>
            <div className="absolute right-0 bottom-0 transform translate-x-4 translate-y-2">
              <Image
                src="/car-enhanced.svg"
                alt="Car"
                width={150}
                height={125}
                className="object-contain"
              />
            </div>
            {/* Decorative circles */}
            <div className="absolute top-4 right-20 w-16 h-16 rounded-full bg-white opacity-10"></div>
            <div className="absolute top-16 right-12 w-10 h-10 rounded-full bg-white opacity-10"></div>
            <div className="absolute bottom-20 left-10 w-20 h-20 rounded-full bg-white opacity-5"></div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="px-4 mt-12">
        <h3 className="text-[#333333] font-semibold mb-3">Quick Actions</h3>
        <div className="grid grid-cols-3 gap-3 p-4 bg-white rounded-xl shadow-lg border border-gray-100">
          <button
            className="flex flex-col items-center"
            onClick={() => router.push("/vehicle-dashboard")}
          >
            <div className="bg-[#009639] w-12 h-12 rounded-full flex items-center justify-center shadow-sm mb-1">
              <Car size={20} className="text-white" />
            </div>
            <span className="text-xs text-[#333333] font-medium">Vehicles</span>
          </button>
          <button
            className="flex flex-col items-center"
            onClick={() => router.push("/group-finances/1")}
          >
            <div className="bg-[#007A2F] w-12 h-12 rounded-full flex items-center justify-center shadow-sm mb-1">
              <Wallet size={20} className="text-white" />
            </div>
            <span className="text-xs text-[#333333] font-medium">Finances</span>
          </button>
          <button
            className="flex flex-col items-center"
            onClick={() => router.push("/help")}
          >
            <div className="bg-[#FFD700] w-12 h-12 rounded-full flex items-center justify-center shadow-sm mb-1">
              <Bell size={20} className="text-[#333333]" />
            </div>
            <span className="text-xs text-[#333333] font-medium">Help</span>
          </button>
        </div>
      </div>

      {/* Upcoming Events */}
      <div className="px-4 mt-12">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-[#333333] font-semibold">Upcoming</h3>
          <button
            className="text-[#009639] text-sm font-medium flex items-center"
            onClick={() => router.push("/calendar-view")}
          >
            Calendar <ChevronRight size={16} />
          </button>
        </div>
        <div className="p-1 bg-transparent">
          {upcomingEvents.map((event) => (
            <div
              key={event.id}
              className={`p-4 flex items-start bg-white rounded-lg drop-shadow-sm mb-3 mx-1 border border-gray-100`}
            >
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                {event.icon}
              </div>
              <div className="flex-1">
                <h4 className="text-[#333333] font-medium">{event.title}</h4>
                <p className="text-xs text-[#797879]">{event.description}</p>
                <div className="flex items-center mt-1">
                  <Clock size={12} className="text-[#797879] mr-1" />
                  <p className="text-xs text-[#797879]">{event.time}</p>
                </div>
              </div>
              <button className="text-[#009639]">
                <ChevronRight size={20} />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Featured Opportunities */}
      <div className="px-4 mt-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-[#333333] font-semibold">
            Featured Opportunities
          </h3>
          <button
            className="text-[#009639] text-sm font-medium flex items-center"
            onClick={() => router.push("/opportunities")}
          >
            View All <ChevronRight size={16} />
          </button>
        </div>

        <div className="flex space-x-4 overflow-x-auto pb-4">
          {opportunities.map((opportunity) => (
            <div
              key={opportunity.id}
              className="ride-card min-w-[250px] overflow-hidden drop-shadow-sm rounded-xl border border-gray-100"
            >
              <div className="h-32 bg-[#f2f2f2] relative rounded-t-xl">
                <Image
                  src={opportunity.image || "/placeholder.svg"}
                  alt={opportunity.title}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-3">
                <h4 className="text-[#333333] font-medium">
                  {opportunity.title}
                </h4>
                <p className="text-xs text-[#797879] mt-1">
                  {opportunity.description}
                </p>
                <button
                  className="ride-primary-btn w-full text-sm py-2 mt-3"
                  onClick={() =>
                    router.push(`/opportunities/${opportunity.id}`)
                  }
                >
                  View Details
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="px-4 mt-6 mb-8">
        <h3 className="text-[#333333] font-semibold mb-4">Recent Activity</h3>
        <div className="p-1 bg-transparent">
          {recentActivities.map((activity) => (
            <div
              key={activity.id}
              className={`p-4 flex items-center bg-white rounded-lg drop-shadow-sm mb-3 mx-1 border border-gray-100`}
            >
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                {activity.type === "booking" ? (
                  <Car size={20} className="text-[#009639]" />
                ) : activity.type === "payment" ? (
                  <Wallet size={20} className="text-[#009639]" />
                ) : activity.type === "group" ? (
                  <Users size={20} className="text-[#009639]" />
                ) : (
                  <Activity size={20} className="text-[#009639]" />
                )}
              </div>
              <div className="flex-1">
                <h4 className="text-[#333333] text-sm font-medium">
                  {activity.title}
                </h4>
                <p className="text-xs text-[#797879]">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
