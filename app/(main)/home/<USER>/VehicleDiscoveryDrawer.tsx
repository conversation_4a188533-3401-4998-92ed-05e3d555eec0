"use client";

import { useState } from "react";
import { ChevronRight, FileText, Clock, DollarSign } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  weeklyRate: number;
  image: string;
  requirements: {
    minDeposit: number;
    documents: string[];
  };
  leaseTerms: {
    ownershipTimeline: string;
    paymentOptions: string[];
  };
}

interface VehicleDiscoveryDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onNext: (selectedVehicle: Vehicle) => void;
}

export default function VehicleDiscoveryDrawer({
  isOpen,
  onClose,
  onNext,
}: VehicleDiscoveryDrawerProps) {
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);

  const vehicles: Vehicle[] = [
    {
      id: 1,
      make: "Suzuki",
      model: "Desire",
      year: 2023,
      weeklyRate: 2700,
      image: "/placeholder.svg?height=200&width=300",
      requirements: {
        minDeposit: 7500,
        documents: [
          "ID Document",
          "Bank Statement - 3 months",
          "Proof of residence",
          "Driver's license",
          "PrDP (Professional driving permit)",
          "Operator card or public operating licence",
        ],
      },
      leaseTerms: {
        ownershipTimeline: "36 months",
        paymentOptions: ["Flexible payment options available"],
      },
    },
  ];

  const handleVehicleSelect = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
  };

  const handleNext = () => {
    if (selectedVehicle) {
      onNext(selectedVehicle);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <SheetTitle className="text-xl font-bold text-white">
              Select Vehicle
            </SheetTitle>
            <SheetDescription className="text-sm text-green-100">
              Choose a vehicle for e-hailing lease
            </SheetDescription>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-4">
              {vehicles.map((vehicle) => (
                <div
                  key={vehicle.id}
                  className={`cursor-pointer rounded-xl border-2 p-4 transition-all ${
                    selectedVehicle?.id === vehicle.id
                      ? "border-[#009639] bg-[#f8fff8]"
                      : "border-gray-200 bg-white hover:border-[#009639] hover:bg-[#f8fff8]"
                  }`}
                  onClick={() => handleVehicleSelect(vehicle)}
                >
                  {/* Vehicle Image */}
                  <div className="mb-3 h-32 overflow-hidden rounded-lg bg-gray-100">
                    <img
                      src={vehicle.image}
                      alt={`${vehicle.make} ${vehicle.model}`}
                      className="h-full w-full object-cover"
                    />
                  </div>

                  {/* Vehicle Info */}
                  <div className="mb-3">
                    <h3 className="font-semibold text-[#333333]">
                      {vehicle.make} {vehicle.model}
                    </h3>
                    <p className="text-sm text-[#797879]">
                      {vehicle.year} Model
                    </p>
                  </div>

                  {/* Weekly Rate */}
                  <div className="mb-3 flex items-center justify-between">
                    <span className="text-sm text-[#797879]">Weekly Rate</span>
                    <span className="text-lg font-bold text-[#009639]">
                      R{vehicle.weeklyRate.toLocaleString()}
                    </span>
                  </div>

                  {/* Initial Fee */}
                  <div className="mb-4 rounded-lg bg-[#f8fff8] border border-[#e6ffe6] p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-[#797879]">
                        Initial Fee
                      </span>
                      <span className="text-lg font-bold text-[#009639]">
                        R{vehicle.requirements.minDeposit.toLocaleString()}
                      </span>
                    </div>
                    <p className="text-xs text-[#797879]">
                      Flexible payment options available
                    </p>
                  </div>

                  {/* Required Documents */}
                  <div className="mb-4">
                    <div className="flex items-center mb-2">
                      <FileText size={16} className="mr-2 text-[#009639]" />
                      <span className="text-sm font-medium text-[#333333]">
                        Required Documents
                      </span>
                    </div>
                    <div className="space-y-1">
                      {vehicle.requirements.documents.map((doc, index) => (
                        <div
                          key={index}
                          className="flex items-center text-xs text-[#797879]"
                        >
                          <div className="w-1 h-1 bg-[#009639] rounded-full mr-2"></div>
                          {doc}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Process Overview */}
                  <div className="mb-4">
                    <div className="flex items-center mb-2">
                      <Clock size={16} className="mr-2 text-[#009639]" />
                      <span className="text-sm font-medium text-[#333333]">
                        Process Overview
                      </span>
                    </div>
                    <div className="space-y-2">
                      {[
                        "Submit application",
                        "Receive approval",
                        "Pay initiation fee",
                        "Sign lease",
                        "Receive car",
                      ].map((step, index) => (
                        <div
                          key={index}
                          className="flex items-center text-xs text-[#797879]"
                        >
                          <div className="w-4 h-4 bg-[#009639] text-white rounded-full flex items-center justify-center mr-2 text-[10px] font-medium">
                            {index + 1}
                          </div>
                          {step}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Lease Terms */}
                  <div className="mb-3 rounded-lg bg-gray-50 p-3">
                    <div className="flex items-center">
                      <Clock size={16} className="mr-2 text-[#009639]" />
                      <span className="text-sm text-[#797879]">
                        {vehicle.leaseTerms.ownershipTimeline} lease term
                      </span>
                    </div>
                  </div>

                  {/* Selection Indicator */}
                  {selectedVehicle?.id === vehicle.id && (
                    <div className="flex items-center justify-center rounded-lg bg-[#009639] py-2">
                      <span className="text-sm font-medium text-white">
                        Selected
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <button
              onClick={handleNext}
              disabled={!selectedVehicle}
              className={`flex w-full items-center justify-center rounded-full py-3 font-semibold transition-all ${
                selectedVehicle
                  ? "bg-[#009639] text-white hover:bg-[#007A2F]"
                  : "bg-gray-200 text-gray-400 cursor-not-allowed"
              }`}
            >
              Next
              <ChevronRight size={20} className="ml-2" />
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
