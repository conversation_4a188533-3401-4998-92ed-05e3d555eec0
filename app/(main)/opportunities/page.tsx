"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import {
  Search,
  Filter,
  ChevronRight,
  Star,
  Briefcase,
  Shield,
} from "lucide-react";

export default function OpportunitiesScreen() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("vehicles");

  // Handle tab query parameter
  useEffect(() => {
    const tabParam = searchParams.get("tab");
    if (tabParam && ["vehicles", "business", "partners"].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [searchParams]);

  const vehicleListings = [
    {
      id: 1,
      title: "Toyota Hilux",
      description: "Reliable bakkie for all terrains",
      price: "R150,000",
      ownership: "25%",
      location: "Cape Town",
      image: "/placeholder.svg?height=120&width=200",
      rating: 4.8,
      reviews: 24,
    },
    {
      id: 2,
      title: "Volkswagen Polo",
      description: "Economical hatchback for city driving",
      price: "R120,000",
      ownership: "33%",
      location: "Johannesburg",
      image: "/placeholder.svg?height=120&width=200",
      rating: 4.6,
      reviews: 18,
    },
    {
      id: 3,
      title: "Ford Ranger",
      description: "Powerful bakkie for work and leisure",
      price: "R180,000",
      ownership: "50%",
      location: "Durban",
      image: "/placeholder.svg?height=120&width=200",
      rating: 4.9,
      reviews: 32,
    },
  ];

  const businessOpportunities = [
    {
      id: 1,
      title: "Ride-sharing Partnership",
      description: "Use your vehicle for ride-sharing during idle hours",
      category: "Transportation",
      earnings: "R5,000-12,000/month",
      image: "/placeholder.svg?height=120&width=200",
    },
    {
      id: 2,
      title: "Food Delivery Service",
      description: "Partner with local restaurants for food delivery",
      category: "Food & Beverage",
      earnings: "R3,000-8,000/month",
      image: "/placeholder.svg?height=120&width=200",
    },
    {
      id: 3,
      title: "Corporate Transportation",
      description: "Provide transportation for corporate clients",
      category: "Business",
      earnings: "R8,000-20,000/month",
      image: "/placeholder.svg?height=120&width=200",
    },
  ];

  const partnerServices = [
    {
      id: 1,
      title: "Premium Insurance",
      description: "Comprehensive coverage for shared vehicles",
      provider: "Santam Insurance",
      icon: <Shield size={24} className="text-[#009639]" />,
    },
    {
      id: 2,
      title: "Mobile Maintenance",
      description: "On-demand vehicle maintenance and repairs",
      provider: "AutoZone Mobile Services",
      icon: <Briefcase size={24} className="text-[#009639]" />,
    },
    {
      id: 3,
      title: "Business Consulting",
      description: "Financial and legal advice for vehicle sharing",
      provider: "Standard Bank Business",
      icon: <Star size={24} className="text-[#009639]" />,
    },
  ];

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#ffffff"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 border-b border-[#007A2F]">
        <h1 className="text-xl font-bold text-white">Opportunities</h1>
      </div>

      {/* Search and Filter */}
      <div className="p-4">
        <div className="flex space-x-2">
          <div className="flex-1 bg-white rounded-full px-4 py-2 flex items-center shadow-sm">
            <Search size={18} className="text-[#797879] mr-2" />
            <input
              type="text"
              placeholder="Search opportunities"
              className="flex-1 bg-transparent focus:outline-none text-[#333333] text-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <button className="bg-white p-2 rounded-full shadow-sm">
            <Filter size={18} className="text-[#333333]" />
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="px-4">
        <div className="flex border-b border-[#f2f2f2] mb-4">
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "vehicles"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("vehicles")}
          >
            Vehicle Listings
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "business"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("business")}
          >
            Business
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "partners"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("partners")}
          >
            Partners
          </button>
        </div>
      </div>

      {/* Vehicle Listings Tab */}
      {activeTab === "vehicles" && (
        <div className="px-4 pb-8">
          <div className="space-y-4">
            {vehicleListings.map((listing) => (
              <div key={listing.id} className="ride-card overflow-hidden">
                <div className="h-40 bg-[#f2f2f2] relative">
                  <Image
                    src={listing.image || "/placeholder.svg"}
                    alt={listing.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-3 right-3 bg-white px-3 py-1 rounded-full text-xs font-medium text-[#333333] shadow-sm">
                    {listing.ownership} Ownership
                  </div>
                  <div className="absolute bottom-3 left-3 bg-white px-3 py-1 rounded-full text-xs font-medium shadow-sm flex items-center">
                    <Star size={12} className="text-[#ff5c00] mr-1" />
                    <span>{listing.rating}</span>
                    <span className="text-[#797879] ml-1">
                      ({listing.reviews})
                    </span>
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex justify-between items-center mb-1">
                    <h3 className="text-[#333333] font-semibold">
                      {listing.title}
                    </h3>
                    <span className="text-lg font-bold text-[#009639]">
                      {listing.price}
                    </span>
                  </div>

                  <p className="text-sm text-[#797879] mb-2">
                    {listing.description}
                  </p>

                  <div className="flex items-center mb-3">
                    <div className="text-xs text-[#797879]">
                      📍 {listing.location}
                    </div>
                  </div>

                  <button
                    className="ride-primary-btn w-full py-2 text-sm"
                    onClick={() =>
                      router.push(`/fraction-purchase/${listing.id}`)
                    }
                  >
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Business Opportunities Tab */}
      {activeTab === "business" && (
        <div className="px-4 pb-8">
          <div className="space-y-4">
            {businessOpportunities.map((opportunity) => (
              <div key={opportunity.id} className="ride-card overflow-hidden">
                <div className="h-32 bg-[#f2f2f2] relative">
                  <Image
                    src={opportunity.image || "/placeholder.svg"}
                    alt={opportunity.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-3 right-3 bg-white px-3 py-1 rounded-full text-xs font-medium text-[#333333] shadow-sm">
                    {opportunity.category}
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex justify-between items-center mb-1">
                    <h3 className="text-[#333333] font-semibold">
                      {opportunity.title}
                    </h3>
                  </div>

                  <p className="text-sm text-[#797879] mb-2">
                    {opportunity.description}
                  </p>

                  <div className="flex items-center justify-between mb-3">
                    <div className="text-xs text-[#797879]">
                      Potential Earnings
                    </div>
                    <div className="text-sm font-bold text-[#009639]">
                      {opportunity.earnings}
                    </div>
                  </div>

                  <button
                    className="ride-primary-btn w-full py-2 text-sm"
                    onClick={() =>
                      router.push(`/business-opportunity/${opportunity.id}`)
                    }
                  >
                    Apply Now
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Partner Services Tab */}
      {activeTab === "partners" && (
        <div className="px-4 pb-8">
          <div className="space-y-3">
            {partnerServices.map((service) => (
              <div key={service.id} className="ride-card p-4">
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    {service.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="text-[#333333] font-medium">
                          {service.title}
                        </h4>
                        <p className="text-xs text-[#797879] mb-1">
                          {service.provider}
                        </p>
                        <p className="text-sm text-[#333333]">
                          {service.description}
                        </p>
                      </div>
                    </div>
                  </div>
                  <button className="text-[#009639] flex-shrink-0">
                    <ChevronRight size={20} className="text-[#009639]" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
