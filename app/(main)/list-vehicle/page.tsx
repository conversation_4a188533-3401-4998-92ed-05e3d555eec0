"use client";

import type React from "react";

import { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  Upload,
  DollarSign,
  Percent,
  FileText,
  ChevronDown,
  ChevronUp,
  X,
} from "lucide-react";

export default function ListVehicleScreen() {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [vehicleImages, setVehicleImages] = useState<string[]>([]);
  const [showFinanceDetails, setShowFinanceDetails] = useState(false);
  const [showTerms, setShowTerms] = useState(false);
  const [formData, setFormData] = useState({
    make: "",
    model: "",
    year: "",
    color: "",
    mileage: "",
    condition: "excellent",
    location: "",
    description: "",
    ownership: "full", // full, partial, financed
    financeDetails: {
      lender: "",
      monthlyPayment: "",
      remainingBalance: "",
      monthsRemaining: "",
    },
    fractionSize: "equal", // equal, custom
    fractions: 4,
    pricePerFraction: "",
    termsAccepted: false,
  });

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setFormData({
        ...formData,
        [parent]: {
          ...(formData[parent as keyof typeof formData] as Record<
            string,
            string
          >),
          [child]: value,
        },
      });
    } else {
      setFormData({
        ...formData,
        [name]:
          type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
      });
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const newImages = Array.from(files).map((file) =>
        URL.createObjectURL(file)
      );
      setVehicleImages([...vehicleImages, ...newImages]);
    }
  };

  const handleRemoveImage = (index: number) => {
    const newImages = [...vehicleImages];
    newImages.splice(index, 1);
    setVehicleImages(newImages);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Vehicle listing submitted:", { ...formData, vehicleImages });
    router.push("/vehicle-fractions-marketplace");
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">List Vehicle</h1>
      </div>

      <form onSubmit={handleSubmit} className="p-4 space-y-4">
        {/* Vehicle Images */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-[#333333] font-medium">Vehicle Photos</h3>
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              multiple
              onChange={handleImageUpload}
            />
            <button
              type="button"
              className="bg-[#009639] text-white p-2 rounded-full shadow-sm"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload size={20} />
            </button>
          </div>

          {vehicleImages.length > 0 ? (
            <div className="grid grid-cols-3 gap-2">
              {vehicleImages.map((image, index) => (
                <div
                  key={index}
                  className="relative h-24 bg-[#f2f2f2] rounded-lg overflow-hidden"
                >
                  <Image
                    src={image || "/placeholder.svg"}
                    alt={`Vehicle photo ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                  <button
                    type="button"
                    className="absolute top-1 right-1 bg-white rounded-full p-1"
                    onClick={() => handleRemoveImage(index)}
                  >
                    <X size={14} className="text-red-500" />
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <div
              className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload size={32} className="text-[#009639] mb-2" />
              <p className="text-[#797879] text-center">
                Upload photos of your vehicle (min. 3 photos)
              </p>
            </div>
          )}
        </div>

        {/* Vehicle Details */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-4">Vehicle Details</h3>

          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label
                  htmlFor="make"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Make
                </label>
                <input
                  type="text"
                  id="make"
                  name="make"
                  value={formData.make}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  placeholder="e.g. Toyota, Volkswagen"
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="model"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Model
                </label>
                <input
                  type="text"
                  id="model"
                  name="model"
                  value={formData.model}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  placeholder="e.g. Fortuner, Polo"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label
                  htmlFor="year"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Year
                </label>
                <input
                  type="text"
                  id="year"
                  name="year"
                  value={formData.year}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  placeholder="e.g. 2020"
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="color"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Color
                </label>
                <input
                  type="text"
                  id="color"
                  name="color"
                  value={formData.color}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  placeholder="e.g. Blue"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label
                  htmlFor="mileage"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Mileage
                </label>
                <input
                  type="text"
                  id="mileage"
                  name="mileage"
                  value={formData.mileage}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  placeholder="e.g. 25,000 km"
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="condition"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Condition
                </label>
                <select
                  id="condition"
                  name="condition"
                  value={formData.condition}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm bg-white"
                  required
                >
                  <option value="excellent">Excellent</option>
                  <option value="good">Good</option>
                  <option value="fair">Fair</option>
                  <option value="poor">Poor</option>
                </select>
              </div>
            </div>

            <div>
              <label
                htmlFor="location"
                className="block text-sm text-[#797879] mb-1"
              >
                Location
              </label>
              <input
                type="text"
                id="location"
                name="location"
                value={formData.location}
                onChange={handleChange}
                className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                placeholder="e.g. Cape Town, Western Cape"
                required
              />
            </div>

            <div>
              <label
                htmlFor="description"
                className="block text-sm text-[#797879] mb-1"
              >
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm min-h-[100px]"
                placeholder="Describe your vehicle..."
                required
              />
            </div>
          </div>
        </div>

        {/* Current Ownership Status */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-4">
            Current Ownership Status
          </h3>

          <div className="space-y-3">
            <div className="flex space-x-3">
              <button
                type="button"
                className={`flex-1 py-2 px-3 rounded-lg border ${
                  formData.ownership === "full"
                    ? "border-[#009639] bg-[#e6ffe6]"
                    : "border-[#d6d9dd]"
                }`}
                onClick={() => setFormData({ ...formData, ownership: "full" })}
              >
                <p
                  className={`text-sm font-medium ${
                    formData.ownership === "full"
                      ? "text-[#009639]"
                      : "text-[#333333]"
                  }`}
                >
                  Full Ownership
                </p>
              </button>
              <button
                type="button"
                className={`flex-1 py-2 px-3 rounded-lg border ${
                  formData.ownership === "partial"
                    ? "border-[#009639] bg-[#e6ffe6]"
                    : "border-[#d6d9dd]"
                }`}
                onClick={() =>
                  setFormData({ ...formData, ownership: "partial" })
                }
              >
                <p
                  className={`text-sm font-medium ${
                    formData.ownership === "partial"
                      ? "text-[#009639]"
                      : "text-[#333333]"
                  }`}
                >
                  Partial Ownership
                </p>
              </button>
              <button
                type="button"
                className={`flex-1 py-2 px-3 rounded-lg border ${
                  formData.ownership === "financed"
                    ? "border-[#009639] bg-[#e6ffe6]"
                    : "border-[#d6d9dd]"
                }`}
                onClick={() =>
                  setFormData({ ...formData, ownership: "financed" })
                }
              >
                <p
                  className={`text-sm font-medium ${
                    formData.ownership === "financed"
                      ? "text-[#009639]"
                      : "text-[#333333]"
                  }`}
                >
                  Financed
                </p>
              </button>
            </div>

            {formData.ownership === "financed" && (
              <div>
                <button
                  type="button"
                  className="w-full flex items-center justify-between py-2"
                  onClick={() => setShowFinanceDetails(!showFinanceDetails)}
                >
                  <span className="text-[#333333] font-medium">
                    Finance Information
                  </span>
                  {showFinanceDetails ? (
                    <ChevronUp size={20} />
                  ) : (
                    <ChevronDown size={20} />
                  )}
                </button>

                {showFinanceDetails && (
                  <div className="space-y-3 mt-3">
                    <div>
                      <label
                        htmlFor="lender"
                        className="block text-sm text-[#797879] mb-1"
                      >
                        Lender
                      </label>
                      <input
                        type="text"
                        id="lender"
                        name="financeDetails.lender"
                        value={formData.financeDetails.lender}
                        onChange={handleChange}
                        className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                        placeholder="e.g. FNB, Standard Bank"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label
                          htmlFor="monthlyPayment"
                          className="block text-sm text-[#797879] mb-1"
                        >
                          Monthly Payment
                        </label>
                        <input
                          type="text"
                          id="monthlyPayment"
                          name="financeDetails.monthlyPayment"
                          value={formData.financeDetails.monthlyPayment}
                          onChange={handleChange}
                          className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                          placeholder="e.g. R3500"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="remainingBalance"
                          className="block text-sm text-[#797879] mb-1"
                        >
                          Remaining Balance
                        </label>
                        <input
                          type="text"
                          id="remainingBalance"
                          name="financeDetails.remainingBalance"
                          value={formData.financeDetails.remainingBalance}
                          onChange={handleChange}
                          className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                          placeholder="e.g. R150,000"
                        />
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="monthsRemaining"
                        className="block text-sm text-[#797879] mb-1"
                      >
                        Months Remaining
                      </label>
                      <input
                        type="text"
                        id="monthsRemaining"
                        name="financeDetails.monthsRemaining"
                        value={formData.financeDetails.monthsRemaining}
                        onChange={handleChange}
                        className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                        placeholder="e.g. 36"
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Fraction Size Options */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center mb-4">
            <Percent size={20} className="text-[#009639] mr-2" />
            <h3 className="text-[#333333] font-medium">
              Fraction Size Options
            </h3>
          </div>

          <div className="space-y-3">
            <div className="flex space-x-3">
              <button
                type="button"
                className={`flex-1 py-2 px-3 rounded-lg border ${
                  formData.fractionSize === "equal"
                    ? "border-[#009639] bg-[#e6ffe6]"
                    : "border-[#d6d9dd]"
                }`}
                onClick={() =>
                  setFormData({ ...formData, fractionSize: "equal" })
                }
              >
                <p
                  className={`text-sm font-medium ${
                    formData.fractionSize === "equal"
                      ? "text-[#009639]"
                      : "text-[#333333]"
                  }`}
                >
                  Equal Fractions
                </p>
              </button>
              <button
                type="button"
                className={`flex-1 py-2 px-3 rounded-lg border ${
                  formData.fractionSize === "custom"
                    ? "border-[#009639] bg-[#e6ffe6]"
                    : "border-[#d6d9dd]"
                }`}
                onClick={() =>
                  setFormData({ ...formData, fractionSize: "custom" })
                }
              >
                <p
                  className={`text-sm font-medium ${
                    formData.fractionSize === "custom"
                      ? "text-[#009639]"
                      : "text-[#333333]"
                  }`}
                >
                  Custom Fractions
                </p>
              </button>
            </div>

            {formData.fractionSize === "equal" && (
              <div>
                <label
                  htmlFor="fractions"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Number of Equal Fractions
                </label>
                <select
                  id="fractions"
                  name="fractions"
                  value={formData.fractions}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm bg-white"
                >
                  <option value="2">2 equal shares (50% each)</option>
                  <option value="3">3 equal shares (33.3% each)</option>
                  <option value="4">4 equal shares (25% each)</option>
                  <option value="5">5 equal shares (20% each)</option>
                  <option value="10">10 equal shares (10% each)</option>
                </select>
              </div>
            )}

            {formData.fractionSize === "custom" && (
              <div className="p-3 bg-[#f9f9f9] rounded-lg">
                <p className="text-sm text-[#797879] mb-2">
                  Custom fraction sizes will be configured in the next step.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Pricing */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center mb-4">
            <DollarSign size={20} className="text-[#009639] mr-2" />
            <h3 className="text-[#333333] font-medium">Pricing per Fraction</h3>
          </div>

          <div>
            <div className="relative">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                <span className="text-[#797879] font-medium">R</span>
              </div>
              <input
                type="text"
                id="pricePerFraction"
                name="pricePerFraction"
                value={formData.pricePerFraction}
                onChange={handleChange}
                className="w-full pl-10 pr-4 py-3 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                placeholder="Price per fraction in Rand"
                required
              />
            </div>
            <p className="text-xs text-[#797879] mt-2">
              This is the price for each {100 / Number(formData.fractions)}%
              share of the vehicle.
            </p>
          </div>
        </div>

        {/* Terms and Conditions */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <button
            type="button"
            className="w-full flex items-center justify-between"
            onClick={() => setShowTerms(!showTerms)}
          >
            <div className="flex items-center">
              <FileText size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">
                Terms and Conditions
              </h3>
            </div>
            {showTerms ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
          </button>

          {showTerms && (
            <div className="mt-3 p-3 bg-[#f9f9f9] rounded-lg max-h-40 overflow-y-auto">
              <p className="text-sm text-[#797879]">
                By listing your vehicle on Poolly, you agree to the following
                terms:
                <br />
                <br />
                1. You confirm that all information provided about the vehicle
                is accurate and complete.
                <br />
                <br />
                2. You have the legal right to sell or offer fractions of this
                vehicle in accordance with South African law.
                <br />
                <br />
                3. You agree to the standard Poolly co-ownership agreement that
                will govern the relationship between all co-owners in South
                Africa.
                <br />
                <br />
                4. Poolly will charge a 5% service fee on the total transaction
                value when fractions are sold in Rand (ZAR).
                <br />
                <br />
                5. You agree to maintain the vehicle in good condition according
                to South African roadworthiness standards until all fractions
                are sold.
              </p>
            </div>
          )}

          <div className="mt-3 flex items-start">
            <input
              type="checkbox"
              id="termsAccepted"
              name="termsAccepted"
              checked={formData.termsAccepted}
              onChange={(e) =>
                setFormData({ ...formData, termsAccepted: e.target.checked })
              }
              className="mt-1 h-4 w-4 rounded border-[#d6d9dd] text-[#009639] focus:ring-[#009639]"
              required
            />
            <label
              htmlFor="termsAccepted"
              className="ml-2 text-sm text-[#797879]"
            >
              I agree to the terms and conditions for listing my vehicle on
              Poolly
            </label>
          </div>
        </div>
      </form>

      {/* List Vehicle Button */}
      <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2]">
        <button
          type="submit"
          className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-4 rounded-full text-xl font-semibold shadow-md"
          onClick={handleSubmit}
          disabled={!formData.termsAccepted}
        >
          List Vehicle
        </button>
      </div>
    </div>
  );
}
