"use client";

import { useState, use, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { ArrowLeft, Search, Plus, Check, X, Mail, Clock, AlertCircle } from "lucide-react";
import { getGroupInvitations, cancelInvitation, inviteMembersToGroup } from "../../../../drizzle-actions/community";

interface GroupInvitation {
  id: number;
  email: string;
  name: string;
  firstName: string | null;
  lastName: string | null;
  fraction: number;
  status: string;
  sentAt: string | null;
}

export default function AddMembersScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const groupId = parseInt(use(params).id);
  const [searchQuery, setSearchQuery] = useState("");
  const [invitations, setInvitations] = useState<GroupInvitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [newInviteEmail, setNewInviteEmail] = useState("");
  const [newInviteFirstName, setNewInviteFirstName] = useState("");
  const [newInviteLastName, setNewInviteLastName] = useState("");
  const [newInviteFraction, setNewInviteFraction] = useState(10);
  const [inviting, setInviting] = useState(false);

  // Mock data for group name - in real app this would come from the group details
  const group = {
    id: groupId,
    name: `Group ${groupId}`,
  };

  useEffect(() => {
    const fetchInvitations = async () => {
      try {
        setLoading(true);
        const invitationList = await getGroupInvitations(groupId);
        setInvitations(invitationList);
      } catch (error) {
        console.error("Failed to fetch invitations:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchInvitations();
  }, [groupId]);

  const filteredInvitations = invitations.filter(
    (invitation) =>
      invitation.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invitation.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCancelInvitation = async (invitationId: number) => {
    if (!confirm("Are you sure you want to cancel this invitation?")) {
      return;
    }

    try {
      const result = await cancelInvitation(invitationId);
      if (result.success) {
        setInvitations(invitations.filter(inv => inv.id !== invitationId));
        alert("Invitation cancelled successfully");
      } else {
        alert(result.message || "Failed to cancel invitation");
      }
    } catch (error) {
      console.error("Error cancelling invitation:", error);
      alert("Failed to cancel invitation");
    }
  };

  const handleSendInvitation = async () => {
    if (!newInviteEmail.trim() || !newInviteFirstName.trim() || !newInviteLastName.trim()) {
      alert("Please fill in all fields");
      return;
    }

    if (invitations.some(inv => inv.email === newInviteEmail)) {
      alert("This email has already been invited");
      return;
    }

    try {
      setInviting(true);
      const result = await inviteMembersToGroup(groupId, [{
        email: newInviteEmail.trim(),
        firstName: newInviteFirstName.trim(),
        lastName: newInviteLastName.trim(),
        fraction: newInviteFraction / 100,
      }]);

      if (result.success) {
        // Refresh invitations list
        const updatedInvitations = await getGroupInvitations(groupId);
        setInvitations(updatedInvitations);
        
        // Reset form
        setNewInviteEmail("");
        setNewInviteFirstName("");
        setNewInviteLastName("");
        setNewInviteFraction(10);
        setShowInviteForm(false);
        
        alert("Invitation sent successfully");
      } else {
        alert(result.message || "Failed to send invitation");
      }
    } catch (error) {
      console.error("Error sending invitation:", error);
      alert("Failed to send invitation");
    } finally {
      setInviting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "SENT":
        return <Clock size={16} className="text-[#7A6500]" />;
      case "ACCEPTED":
        return <Check size={16} className="text-[#009639]" />;
      case "DECLINED":
        return <X size={16} className="text-red-500" />;
      default:
        return <AlertCircle size={16} className="text-[#797879]" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "SENT":
        return "bg-[#fff8e6] text-[#7A6500]";
      case "ACCEPTED":
        return "bg-[#e6ffe6] text-[#009639]";
      case "DECLINED":
        return "bg-[#ffe6e6] text-red-500";
      default:
        return "bg-[#f9f9f9] text-[#797879]";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#f5f5f5]">
        <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Manage Invitations</h1>
        </div>
        <div className="p-6">
          <div className="bg-white rounded-xl p-4 animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">
            Manage Invitations
          </h1>
        </div>
        <button
          className="w-10 h-10 bg-[#007A2F] rounded-full flex items-center justify-center shadow-sm"
          onClick={() => setShowInviteForm(!showInviteForm)}
        >
          <Plus size={24} className="text-white" />
        </button>
      </div>

      {/* Invite Form */}
      {showInviteForm && (
        <div className="p-4 bg-white border-b border-[#f2f2f2]">
          <h3 className="text-[#333333] font-medium mb-4">Send New Invitation</h3>
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <input
                type="text"
                value={newInviteFirstName}
                onChange={(e) => setNewInviteFirstName(e.target.value)}
                placeholder="First name"
                className="px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
              <input
                type="text"
                value={newInviteLastName}
                onChange={(e) => setNewInviteLastName(e.target.value)}
                placeholder="Last name"
                className="px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
            </div>
            <input
              type="email"
              value={newInviteEmail}
              onChange={(e) => setNewInviteEmail(e.target.value)}
              placeholder="Email address"
              className="w-full px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
            />
            <div className="flex items-center space-x-3">
              <label className="text-sm text-[#333333]">Ownership %:</label>
              <input
                type="number"
                min="1"
                max="100"
                value={newInviteFraction}
                onChange={(e) => setNewInviteFraction(parseInt(e.target.value) || 0)}
                className="px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333] w-20"
              />
            </div>
            <div className="flex space-x-3">
              <button
                onClick={handleSendInvitation}
                disabled={inviting || !newInviteEmail.trim() || !newInviteFirstName.trim() || !newInviteLastName.trim()}
                className="flex-1 bg-[#009639] text-white py-2 rounded-lg font-medium disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                {inviting ? "Sending..." : "Send Invitation"}
              </button>
              <button
                onClick={() => setShowInviteForm(false)}
                className="px-4 py-2 border border-[#e5e5e5] rounded-lg text-[#333333]"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Search */}
      <div className="p-4">
        <div className="bg-white rounded-full px-4 py-2 flex items-center shadow-sm">
          <Search size={18} className="text-[#797879] mr-2" />
          <input
            type="text"
            placeholder="Search invitations by name or email"
            className="flex-1 bg-transparent focus:outline-none text-[#333333] text-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* Invitations List */}
      <div className="px-4 pb-24 pt-2">
        <h3 className="text-[#333333] font-medium mb-4">
          Pending Invitations ({filteredInvitations.length})
        </h3>
        
        {filteredInvitations.length === 0 ? (
          <div className="bg-white rounded-xl shadow-md p-8 text-center border border-gray-100">
            {searchQuery ? (
              <p className="text-[#797879]">No invitations match your search</p>
            ) : (
              <>
                <Mail size={48} className="text-[#e5e5e5] mx-auto mb-4" />
                <p className="text-[#797879] mb-4">No pending invitations</p>
                <button
                  onClick={() => setShowInviteForm(true)}
                  className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
                >
                  Send First Invitation
                </button>
              </>
            )}
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
            {filteredInvitations.map((invitation, index) => (
              <div
                key={invitation.id}
                className={`p-4 flex items-center ${
                  index < filteredInvitations.length - 1
                    ? "border-b border-[#f2f2f2]"
                    : ""
                }`}
              >
                <div className="w-10 h-10 bg-[#f5f5f5] rounded-full flex items-center justify-center mr-3">
                  <Mail size={18} className="text-[#009639]" />
                </div>
                <div className="flex-1">
                  <h3 className="text-[#333333] font-medium">{invitation.name}</h3>
                  <p className="text-xs text-[#797879]">{invitation.email}</p>
                  <div className="flex items-center mt-1 space-x-2">
                    <span className={`text-xs px-2 py-0.5 rounded-full flex items-center ${getStatusColor(invitation.status)}`}>
                      {getStatusIcon(invitation.status)}
                      <span className="ml-1">{invitation.status}</span>
                    </span>
                    <span className="text-xs text-[#797879]">
                      {invitation.fraction * 100}% ownership
                    </span>
                    {invitation.sentAt && (
                      <span className="text-xs text-[#797879]">
                        • {new Date(invitation.sentAt).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
                {invitation.status === "SENT" && (
                  <button
                    className="text-red-500 text-sm px-3 py-1 rounded hover:bg-red-50"
                    onClick={() => handleCancelInvitation(invitation.id)}
                  >
                    Cancel
                  </button>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2] shadow-lg">
        <button
          className="w-full bg-[#009639] text-white py-3 rounded-xl font-semibold flex items-center justify-center"
          onClick={() => router.back()}
        >
          Done
        </button>
      </div>
    </div>
  );
}
