"use client";

import { usePathname, useRouter } from "next/navigation";
import { Home, Car, Compass, Users, User } from "lucide-react";

export default function BottomNavigation() {
  const router = useRouter();
  const pathname = usePathname();

  // Helper function to check if a path is active
  const isActive = (path: string) => {
    if (path === "/home" && (pathname === "/" || pathname === "/home"))
      return true;
    return pathname.startsWith(path);
  };

  const navItems = [
    {
      label: "Home",
      icon: Home,
      href: "/home",
      active: isActive("/home"),
    },
    {
      label: "My Vehicles",
      icon: Car,
      href: "/vehicle-dashboard",
      active: isActive("/vehicle-dashboard"),
    },
    {
      label: "Marketplace",
      icon: Compass,
      href: "/opportunities",
      active: isActive("/opportunities"),
    },
    {
      label: "Co Own",
      icon: Users,
      href: "/community",
      active: isActive("/community"),
    },
    {
      label: "Profile",
      icon: User,
      href: "/profile",
      active: isActive("/profile"),
    },
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-[#f2f2f2] h-16 flex items-center justify-around px-2 z-50">
      {navItems.map((item) => (
        <button
          key={item.label}
          className={`flex flex-col items-center justify-center w-full h-full ${
            item.active ? "text-[#009639]" : "text-[#797879]"
          }`}
          onClick={() => router.push(item.href)}
        >
          <item.icon
            size={20}
            className={item.active ? "text-[#009639]" : "text-[#797879]"}
          />
          <span className="text-xs mt-1 font-medium">{item.label}</span>
        </button>
      ))}
    </div>
  );
}
