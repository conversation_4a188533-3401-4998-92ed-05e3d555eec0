import { pgTable, index, unique, serial, varchar, text, boolean, timestamp, foreignKey, integer, numeric, doublePrecision, json, date, primaryKey, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const audienceenum = pgEnum("audienceenum", ['BUSINESS', 'E_HAILING', 'CONSUMER'])
export const bookingstatus = pgEnum("bookingstatus", ['PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED'])
export const cleanlinesslevel = pgEnum("cleanlinesslevel", ['clean', 'acceptable', 'dirty'])
export const commentpriority = pgEnum("commentpriority", ['LOW', 'MEDIUM', 'HIGH'])
export const commentstatus = pgEnum("commentstatus", ['OPEN', 'RESOLVED', 'DELETED'])
export const companyownershipinviteenum = pgEnum("companyownershipinviteenum", ['SENT', 'DECLINED', 'ACCEPTED'])
export const companytypeenum = pgEnum("companytypeenum", ['PRIVATE_COMPANY', 'NON_PROFIT', 'PARTNERSHIP', 'COOPERATIVE'])
export const compliancerequirementstatusenum = pgEnum("compliancerequirementstatusenum", ['ACCEPTED', 'REJECTED', 'INCOMPLETE', 'PENDING'])
export const conditionenum = pgEnum("conditionenum", ['new', 'used'])
export const conditionlevel = pgEnum("conditionlevel", ['none', 'minor', 'major'])
export const dashboardcondition = pgEnum("dashboardcondition", ['working', 'partial', 'issues'])
export const disputestatus = pgEnum("disputestatus", ['OPEN', 'RESOLVED', 'DELETED'])
export const disputetype = pgEnum("disputetype", ['BOOKING', 'vEEHICLE_DAMAGE', 'VEHICLE_MAINTENANCE', 'MAINTENANCE_COST_DISPUTE', 'OTHER'])
export const documenttype = pgEnum("documenttype", ['registration', 'insurance', 'inspection', 'other'])
export const generalcondition = pgEnum("generalcondition", ['good', 'fair', 'poor'])
export const lightscondition = pgEnum("lightscondition", ['working', 'partial', 'broken'])
export const listingtypeenum = pgEnum("listingtypeenum", ['SHORT_TERM_LEASE_OUT', 'LONG_TERM_LEASE_OUT', 'CO_OWNERSHIP_SALE'])
export const odorlevel = pgEnum("odorlevel", ['none', 'mild', 'strong'])
export const phototype = pgEnum("phototype", ['left_view', 'right_view', 'rear_view', 'front_view', 'dashboard', 'seats_view', 'interior', 'additional', 'tires', 'signature'])
export const possessionstatus = pgEnum("possessionstatus", ['PENDING', 'COMPLETED', 'CANCELLED'])
export const priority = pgEnum("priority", ['LOW', 'MEDIUM', 'HIGH'])
export const referencetypeenum = pgEnum("referencetypeenum", ['TAX_PIN', 'URL'])
export const servicestatus = pgEnum("servicestatus", ['SCHEDULED', 'PENDING', 'COMPLETED'])
export const servicetypeenum = pgEnum("servicetypeenum", ['MAINTENANCE', 'INSURANCE', 'CLEANING', 'FUEL', 'OTHER'])
export const vehicleservicestatus = pgEnum("vehicleservicestatus", ['SCHEDULED', 'PENDING', 'COMPLETED'])
export const verificationtypeenum = pgEnum("verificationtypeenum", ['AI', 'MANUAL', 'API'])


export const cities = pgTable("cities", {
	id: serial().primaryKey().notNull(),
	name: varchar().notNull(),
	country: varchar().notNull(),
	province: varchar().notNull(),
}, (table) => [
	index("ix_cities_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	unique("uq_city_name_province").on(table.name, table.province),
]);

export const accountType = pgTable("account_type", {
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("account_type_name_key").on(table.name),
]);

export const alembicVersion = pgTable("alembic_version", {
	versionNum: varchar("version_num", { length: 32 }).primaryKey().notNull(),
});

export const companyOwnership = pgTable("company_ownership", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	companyId: integer("company_id").notNull(),
	fraction: numeric().notNull(),
	effectiveFrom: timestamp("effective_from", { withTimezone: true, mode: 'string' }).notNull(),
	effectiveTo: timestamp("effective_to", { withTimezone: true, mode: 'string' }),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.companyId],
			foreignColumns: [company.id],
			name: "company_ownership_company_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "company_ownership_party_id_fkey"
		}).onDelete("cascade"),
]);

export const complianceSet = pgTable("compliance_set", {
	name: text().notNull(),
	description: text(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
});

export const complianceRequirementType = pgTable("compliance_requirement_type", {
	name: varchar().notNull(),
	description: varchar(),
	defaultValidityInDays: integer("default_validity_in_days"),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("compliance_requirement_type_name_key").on(table.name),
]);

export const addressType = pgTable("address_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("address_type_name_key").on(table.name),
]);

export const bookings = pgTable("bookings", {
	vehicleId: integer("vehicle_id").notNull(),
	reference: varchar({ length: 50 }).notNull(),
	startDatetime: timestamp("start_datetime", { mode: 'string' }).notNull(),
	endDatetime: timestamp("end_datetime", { mode: 'string' }).notNull(),
	status: bookingstatus(),
	totalPrice: doublePrecision("total_price"),
	notes: text(),
	partyId: integer("party_id").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_bookings_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "bookings_party_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "bookings_vehicle_id_fkey"
		}),
	unique("bookings_reference_key").on(table.reference),
]);

export const complianceRequirement = pgTable("compliance_requirement", {
	partyId: integer("party_id").notNull(),
	complianceSetId: integer("compliance_set_id").notNull(),
	requirementTypeId: integer("requirement_type_id").notNull(),
	reviewedById: integer("reviewed_by_id"),
	status: compliancerequirementstatusenum(),
	referenceType: referencetypeenum("reference_type"),
	reference: varchar(),
	submittedAt: timestamp("submitted_at", { mode: 'string' }),
	reviewedAt: timestamp("reviewed_at", { mode: 'string' }),
	notes: text(),
	issueDate: timestamp("issue_date", { mode: 'string' }),
	expiryDate: timestamp("expiry_date", { mode: 'string' }),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	issuingAuthorityId: integer("issuing_authority_id"),
	uploadedIndividualId: integer("uploaded_individual_id"),
	verificationId: integer("verification_id"),
}, (table) => [
	foreignKey({
			columns: [table.complianceSetId],
			foreignColumns: [complianceSet.id],
			name: "compliance_requirement_compliance_set_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.issuingAuthorityId],
			foreignColumns: [issuingAuthority.id],
			name: "compliance_requirement_issuing_authority_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "compliance_requirement_party_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.requirementTypeId],
			foreignColumns: [complianceRequirementType.id],
			name: "compliance_requirement_requirement_type_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.reviewedById],
			foreignColumns: [individual.id],
			name: "compliance_requirement_reviewed_by_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.uploadedIndividualId],
			foreignColumns: [individual.id],
			name: "compliance_requirement_uploaded_individual_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.verificationId],
			foreignColumns: [verification.id],
			name: "compliance_requirement_verification_id_fkey"
		}).onDelete("cascade"),
]);

export const contactType = pgTable("contact_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("contact_type_name_key").on(table.name),
]);

export const contractStatus = pgTable("contract_status", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	displayOrder: integer("display_order"),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("contract_status_name_key").on(table.name),
]);

export const contactPoint = pgTable("contact_point", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	contactPointTypeId: integer("contact_point_type_id").notNull(),
	value: text().notNull(),
	addressTypeId: integer("address_type_id"),
	isPrimary: boolean("is_primary").notNull(),
	isVerified: boolean("is_verified").notNull(),
	verificationDate: timestamp("verification_date", { mode: 'string' }),
	verificationMethod: text("verification_method"),
	mtadata: json(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.addressTypeId],
			foreignColumns: [addressType.id],
			name: "contact_point_address_type_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.contactPointTypeId],
			foreignColumns: [contactPointType.id],
			name: "contact_point_contact_point_type_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "contact_point_party_id_fkey"
		}).onDelete("cascade"),
]);

export const contactPointType = pgTable("contact_point_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	validationPattern: text("validation_pattern"),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("contact_point_type_name_key").on(table.name),
]);

export const contractType = pgTable("contract_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("contract_type_name_key").on(table.name),
]);

export const identificationType = pgTable("identification_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	validationPattern: text("validation_pattern"),
	expirationRequired: boolean("expiration_required"),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("identification_type_name_key").on(table.name),
]);

export const disputeMedia = pgTable("dispute_media", {
	disputeId: integer("dispute_id").notNull(),
	mediaPath: varchar("media_path").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_dispute_media_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.disputeId],
			foreignColumns: [disputes.id],
			name: "dispute_media_dispute_id_fkey"
		}),
]);

export const documentType = pgTable("document_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("document_type_name_key").on(table.name),
]);

export const emergencyContacts = pgTable("emergency_contacts", {
	id: serial().primaryKey().notNull(),
	name: varchar().notNull(),
	phoneNumber: varchar("phone_number").notNull(),
	contactType: varchar("contact_type").notNull(),
	description: varchar(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	index("ix_emergency_contacts_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
]);

export const individual = pgTable("individual", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	firstName: text("first_name").notNull(),
	lastName: text("last_name").notNull(),
	middleName: text("middle_name"),
	salutation: text(),
	suffix: text(),
	gender: text(),
	birthDate: timestamp("birth_date", { mode: 'string' }),
	maritalStatus: text("marital_status"),
	nationality: text(),
	preferredLanguage: text("preferred_language"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "individual_party_id_fkey"
		}).onDelete("cascade"),
]);

export const disputes = pgTable("disputes", {
	name: varchar().notNull(),
	description: text(),
	vehicleId: integer("vehicle_id").notNull(),
	disputeType: disputetype("dispute_type").notNull(),
	partyOffending: integer("party_offending").notNull(),
	partyLogging: integer("party_logging").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	priority: priority(),
	companyId: integer("company_id").notNull(),
	disputeStatus: disputestatus("dispute_status"),
}, (table) => [
	index("ix_disputes_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.partyOffending],
			foreignColumns: [party.id],
			name: "disputes_party_offending_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.partyLogging],
			foreignColumns: [party.id],
			name: "disputes_party_logging_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.companyId],
			foreignColumns: [company.id],
			name: "disputes_company_id_fkey"
		}),
]);

export const industry = pgTable("industry", {
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("industry_name_key").on(table.name),
]);

export const lead = pgTable("lead", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	company: text(),
	title: text(),
	statusId: integer("status_id").notNull(),
	sourceId: integer("source_id"),
	rating: text(),
	annualRevenue: integer("annual_revenue"),
	numberOfEmployees: integer("number_of_employees"),
	industry: text(),
	description: text(),
	isConverted: boolean("is_converted").notNull(),
	convertedDate: timestamp("converted_date", { mode: 'string' }),
	convertedAccountId: integer("converted_account_id"),
	convertedContactId: integer("converted_contact_id"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "lead_party_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.sourceId],
			foreignColumns: [leadSource.id],
			name: "lead_source_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.statusId],
			foreignColumns: [leadStatus.id],
			name: "lead_status_id_fkey"
		}).onDelete("cascade"),
]);

export const leadSource = pgTable("lead_source", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("lead_source_name_key").on(table.name),
]);

export const leadStatus = pgTable("lead_status", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	displayOrder: integer("display_order"),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("lead_status_name_key").on(table.name),
]);

export const matchRuleType = pgTable("match_rule_type", {
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	confidence: integer(),
	isActive: boolean("is_active"),
	priority: integer(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
});

export const opportunityStage = pgTable("opportunity_stage", {
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	probability: integer(),
	displayOrder: integer("display_order"),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("opportunity_stage_name_key").on(table.name),
]);

export const opportunityType = pgTable("opportunity_type", {
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("opportunity_type_name_key").on(table.name),
]);

export const issuingAuthority = pgTable("issuing_authority", {
	partyId: integer("party_id").notNull(),
	name: varchar().notNull(),
	description: varchar(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	country: varchar(),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "issuing_authority_party_id_fkey"
		}).onDelete("cascade"),
	unique("issuing_authority_name_key").on(table.name),
]);

export const partyStatus = pgTable("party_status", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("party_status_name_key").on(table.name),
]);

export const party = pgTable("party", {
	id: serial().primaryKey().notNull(),
	partyTypeId: integer("party_type_id").notNull(),
	statusId: integer("status_id").notNull(),
	externalId: text("external_id"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyTypeId],
			foreignColumns: [partyType.id],
			name: "party_party_type_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.statusId],
			foreignColumns: [partyStatus.id],
			name: "party_status_id_fkey"
		}).onDelete("cascade"),
	unique("party_external_id_key").on(table.externalId),
]);

export const partyIdentification = pgTable("party_identification", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	identificationTypeId: integer("identification_type_id").notNull(),
	documentNumber: text("document_number").notNull(),
	issuingAuthority: text("issuing_authority"),
	issueDate: timestamp("issue_date", { mode: 'string' }),
	expiryDate: timestamp("expiry_date", { mode: 'string' }),
	isVerified: boolean("is_verified").notNull(),
	verificationDate: timestamp("verification_date", { mode: 'string' }),
	verificationMethod: text("verification_method"),
	documentImageUrl: text("document_image_url"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.identificationTypeId],
			foreignColumns: [identificationType.id],
			name: "party_identification_identification_type_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "party_identification_party_id_fkey"
		}).onDelete("cascade"),
]);

export const productType = pgTable("product_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("product_type_name_key").on(table.name),
]);

export const recordType = pgTable("record_type", {
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("record_type_name_key").on(table.name),
]);

export const relationshipType = pgTable("relationship_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("relationship_type_name_key").on(table.name),
]);

export const vehicleInspections = pgTable("vehicle_inspections", {
	scratches: conditionlevel().notNull(),
	dents: conditionlevel().notNull(),
	tires: generalcondition().notNull(),
	lights: lightscondition().notNull(),
	cleanliness: cleanlinesslevel().notNull(),
	seats: generalcondition().notNull(),
	dashboardControls: dashboardcondition("dashboard_controls").notNull(),
	odors: odorlevel().notNull(),
	odometer: integer().notNull(),
	knownIssues: text("known_issues"),
	vehicleId: integer("vehicle_id").notNull(),
	possessionId: integer("possession_id").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_inspections_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.possessionId],
			foreignColumns: [vehiclePossessions.id],
			name: "vehicle_inspections_possession_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "vehicle_inspections_vehicle_id_fkey"
		}).onDelete("cascade"),
]);

export const socialMediaType = pgTable("social_media_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	baseUrl: text("base_url"),
	icon: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("social_media_type_name_key").on(table.name),
]);

export const subscriptionStatus = pgTable("subscription_status", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	displayOrder: integer("display_order"),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("subscription_status_name_key").on(table.name),
]);

export const users = pgTable("users", {
	id: serial().primaryKey().notNull(),
	username: varchar({ length: 50 }).notNull(),
	email: varchar({ length: 100 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	familyName: varchar("family_name", { length: 100 }),
	givenName: varchar("given_name", { length: 100 }),
	phoneNumber: varchar("phone_number", { length: 20 }),
	orgId: varchar("org_id", { length: 16 }),
}, (table) => [
	unique("users_username_key").on(table.username),
	unique("users_email_key").on(table.email),
]);

export const vehicleMaintenance = pgTable("vehicle_maintenance", {
	vehicleId: integer("vehicle_id").notNull(),
	name: varchar({ length: 100 }).notNull(),
	description: varchar({ length: 500 }),
	dueDate: timestamp("due_date", { mode: 'string' }).notNull(),
	dueOdometer: doublePrecision("due_odometer").notNull(),
	status: vehicleservicestatus().notNull(),
	expectedCost: doublePrecision("expected_cost").notNull(),
	completedDate: timestamp("completed_date", { mode: 'string' }),
	completedOdometer: doublePrecision("completed_odometer"),
	actualCost: doublePrecision("actual_cost"),
	technicianNotes: varchar("technician_notes", { length: 1000 }),
	serviceProvider: varchar("service_provider", { length: 1000 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	isScheduled: boolean("is_scheduled"),
	id: serial().notNull(),
}, (table) => [
	index("ix_vehicle_maintenance_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "vehicle_maintenance_vehicle_id_fkey"
		}),
]);

export const vehicleMake = pgTable("vehicle_make", {
	name: varchar().notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_make_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
]);

export const partyType = pgTable("party_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("party_type_name_key").on(table.name),
]);

export const socialProfile = pgTable("social_profile", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	socialMediaTypeId: integer("social_media_type_id").notNull(),
	username: text().notNull(),
	url: text(),
	isPrimary: boolean("is_primary").notNull(),
	isVerified: boolean("is_verified").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "social_profile_party_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.socialMediaTypeId],
			foreignColumns: [socialMediaType.id],
			name: "social_profile_social_media_type_id_fkey"
		}).onDelete("cascade"),
]);

export const vehicleModelMedia = pgTable("vehicle_model_media", {
	vehicleModelId: integer("vehicle_model_id").notNull(),
	mediaPath: varchar("media_path").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_model_media_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.vehicleModelId],
			foreignColumns: [vehicleModel.id],
			name: "vehicle_model_media_vehicle_model_id_fkey"
		}),
]);

export const vehiclePhotos = pgTable("vehicle_photos", {
	inspectionId: integer("inspection_id").notNull(),
	type: phototype().notNull(),
	fileUrl: varchar("file_url").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_photos_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.inspectionId],
			foreignColumns: [vehicleInspections.id],
			name: "vehicle_photos_inspection_id_fkey"
		}).onDelete("cascade"),
]);

export const verification = pgTable("verification", {
	verifyingPartyId: integer("verifying_party_id").notNull(),
	verificationOutcome: varchar("verification_outcome"),
	cost: doublePrecision(),
	verificationType: verificationtypeenum("verification_type"),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.verifyingPartyId],
			foreignColumns: [party.id],
			name: "verification_verifying_party_id_fkey"
		}),
]);

export const vehiclePossessions = pgTable("vehicle_possessions", {
	fromPartyId: integer("from_party_id").notNull(),
	toPartyId: integer("to_party_id").notNull(),
	vehicleId: integer("vehicle_id").notNull(),
	handoverExpectedDatetime: timestamp("handover_expected_datetime", { mode: 'string' }),
	handoverActualDatetime: timestamp("handover_actual_datetime", { mode: 'string' }),
	status: possessionstatus().notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_possessions_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.fromPartyId],
			foreignColumns: [party.id],
			name: "vehicle_possessions_from_party_id_fkey"
		}),
	foreignKey({
			columns: [table.toPartyId],
			foreignColumns: [party.id],
			name: "vehicle_possessions_to_party_id_fkey"
		}),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "vehicle_possessions_vehicle_id_fkey"
		}),
]);

export const vehicleMedia = pgTable("vehicle_media", {
	vehicleId: integer("vehicle_id").notNull(),
	mediaPath: varchar("media_path").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_media_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "vehicle_media_vehicle_id_fkey"
		}),
]);

export const vehicleModel = pgTable("vehicle_model", {
	makeId: integer("make_id").notNull(),
	model: varchar().notNull(),
	yearModel: integer("year_model").notNull(),
	description: text(),
	transmission: varchar(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_model_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.makeId],
			foreignColumns: [vehicleMake.id],
			name: "vehicle_model_make_id_fkey"
		}).onDelete("cascade"),
]);

export const vehicles = pgTable("vehicles", {
	partyId: integer("party_id").notNull(),
	modelId: integer("model_id").notNull(),
	vinNumber: varchar("vin_number").notNull(),
	vehicleRegistration: varchar("vehicle_registration"),
	countryOfRegistration: varchar("country_of_registration"),
	manufacturingYear: integer("manufacturing_year"),
	purchaseDate: timestamp("purchase_date", { withTimezone: true, mode: 'string' }),
	color: varchar(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicles_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.modelId],
			foreignColumns: [vehicleModel.id],
			name: "vehicles_model_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "vehicles_party_id_fkey"
		}).onDelete("cascade"),
	unique("vehicles_vin_number_key").on(table.vinNumber),
]);

export const company = pgTable("company", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	registrationNumber: varchar("registration_number"),
	registrationCountry: varchar("registration_country"),
	registrationDate: timestamp("registration_date", { withTimezone: true, mode: 'string' }),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	alias: varchar(),
	description: varchar(),
	cityId: integer("city_id"),
	purpose: varchar(),
}, (table) => [
	foreignKey({
			columns: [table.cityId],
			foreignColumns: [cities.id],
			name: "company_city_id_fkey"
		}),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "company_party_id_fkey"
		}).onDelete("cascade"),
]);

export const disputeComments = pgTable("dispute_comments", {
	comment: text().notNull(),
	disputeId: integer("dispute_id").notNull(),
	replyToCommentId: integer("reply_to_comment_id"),
	userId: integer("user_id").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_dispute_comments_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.disputeId],
			foreignColumns: [disputes.id],
			name: "dispute_comments_dispute_id_fkey"
		}),
	foreignKey({
			columns: [table.replyToCommentId],
			foreignColumns: [table.id],
			name: "dispute_comments_reply_to_comment_id_fkey"
		}),
]);

export const organization = pgTable("organization", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	name: text().notNull(),
	description: text(),
	industry: text(),
	numberOfEmployees: integer("number_of_employees"),
	annualRevenue: integer("annual_revenue"),
	websiteUrl: text("website_url"),
	logoUrl: text("logo_url"),
	isDeleted: boolean("is_deleted").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "organization_party_id_fkey"
		}).onDelete("cascade"),
]);

export const vehicleDocuments = pgTable("vehicle_documents", {
	id: serial().primaryKey().notNull(),
	vehicleId: integer("vehicle_id").notNull(),
	mediaPath: varchar("media_path").notNull(),
	documentType: documenttype("document_type").notNull(),
	expirationDate: timestamp("expiration_date", { withTimezone: true, mode: 'string' }),
	name: varchar(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_documents_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "vehicle_documents_vehicle_id_fkey"
		}),
]);

export const listingMedia = pgTable("listing_media", {
	listingId: integer("listing_id").notNull(),
	mediaPath: varchar("media_path").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.listingId],
			foreignColumns: [listings.id],
			name: "listing_media_listing_id_fkey"
		}),
]);

export const companyOwnershipInvite = pgTable("company_ownership_invite", {
	id: serial().primaryKey().notNull(),
	companyId: integer("company_id").notNull(),
	fraction: numeric().notNull(),
	status: companyownershipinviteenum().notNull(),
	email: varchar({ length: 100 }).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	firstName: text("first_name"),
	lastName: text("last_name"),
}, (table) => [
	foreignKey({
			columns: [table.companyId],
			foreignColumns: [company.id],
			name: "company_ownership_invite_company_id_fkey"
		}).onDelete("cascade"),
]);

export const complianceSetRequirementTypeMapping = pgTable("compliance_set_requirement_type_mapping", {
	complianceSetId: integer("compliance_set_id").notNull(),
	complianceRequirementTypeId: integer("compliance_requirement_type_id").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.complianceRequirementTypeId],
			foreignColumns: [complianceRequirementType.id],
			name: "compliance_set_requirement_ty_compliance_requirement_type__fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.complianceSetId],
			foreignColumns: [complianceSet.id],
			name: "compliance_set_requirement_type_mapping_compliance_set_id_fkey"
		}).onDelete("cascade"),
]);

export const serviceProviders = pgTable("service_providers", {
	name: varchar().notNull(),
	description: varchar(),
	serviceType: servicetypeenum("service_type").notNull(),
	effectiveFrom: date("effective_from").notNull(),
	effectiveTo: date("effective_to").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
});

export const listings = pgTable("listings", {
	partyId: integer("party_id").notNull(),
	vehicleId: integer("vehicle_id").notNull(),
	effectiveFrom: date("effective_from").notNull(),
	effectiveTo: date("effective_to").notNull(),
	fractionOffer: doublePrecision("fraction_offer").notNull(),
	askingPrice: doublePrecision("asking_price").notNull(),
	condition: conditionenum().notNull(),
	mileage: doublePrecision(),
	listingType: listingtypeenum("listing_type").notNull(),
	audience: audienceenum().notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "listings_party_id_fkey"
		}),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "listings_vehicle_id_fkey"
		}),
]);

export const opportunities = pgTable("opportunities", {
	opportunityName: varchar("opportunity_name").notNull(),
	description: varchar(),
	companyId: integer("company_id").notNull(),
	price: doublePrecision().notNull(),
	fraction: doublePrecision().notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.companyId],
			foreignColumns: [company.id],
			name: "opportunities_company_id_fkey"
		}),
]);

export const votingThreshold = pgTable("voting_threshold", {
	id: integer().notNull(),
	companyId: integer("company_id").notNull(),
	unanimous: boolean(),
	simpleMajority: boolean("simple_majority"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_voting_threshold_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	primaryKey({ columns: [table.id, table.companyId], name: "voting_threshold_pkey"}),
]);

export const companyNotificationPreferences = pgTable("company_notification_preferences", {
	id: integer().notNull(),
	companyId: integer("company_id").notNull(),
	bookingNotifications: boolean("booking_notifications"),
	paymentNotifications: boolean("payment_notifications"),
	maintenanceAlerts: boolean("maintenance_alerts"),
	memberActivity: boolean("member_activity"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_company_notification_preferences_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.companyId],
			foreignColumns: [company.id],
			name: "company_notification_preferences_company_id_fkey"
		}),
	primaryKey({ columns: [table.id, table.companyId], name: "company_notification_preferences_pkey"}),
]);
