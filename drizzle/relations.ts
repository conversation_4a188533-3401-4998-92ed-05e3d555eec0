import { relations } from "drizzle-orm/relations";
import { company, companyOwnership, party, bookings, vehicles, complianceSet, complianceRequirement, issuingAuthority, complianceRequirementType, individual, verification, addressType, contactPoint, contactPointType, disputes, disputeMedia, lead, leadSource, leadStatus, partyType, partyStatus, identificationType, partyIdentification, vehiclePossessions, vehicleInspections, vehicleMaintenance, socialProfile, socialMediaType, vehicleModel, vehicleModelMedia, vehiclePhotos, vehicleMedia, vehicleMake, cities, disputeComments, organization, vehicleDocuments, companyOwnershipInvite, complianceSetRequirementTypeMapping, companyNotificationPreferences } from "./schema";

export const companyOwnershipRelations = relations(companyOwnership, ({one}) => ({
	company: one(company, {
		fields: [companyOwnership.companyId],
		references: [company.id]
	}),
	party: one(party, {
		fields: [companyOwnership.partyId],
		references: [party.id]
	}),
}));

export const companyRelations = relations(company, ({one, many}) => ({
	companyOwnerships: many(companyOwnership),
	disputes: many(disputes),
	city: one(cities, {
		fields: [company.cityId],
		references: [cities.id]
	}),
	party: one(party, {
		fields: [company.partyId],
		references: [party.id]
	}),
	companyOwnershipInvites: many(companyOwnershipInvite),
	companyNotificationPreferences: many(companyNotificationPreferences),
}));

export const partyRelations = relations(party, ({one, many}) => ({
	companyOwnerships: many(companyOwnership),
	bookings: many(bookings),
	complianceRequirements: many(complianceRequirement),
	contactPoints: many(contactPoint),
	individuals: many(individual),
	disputes_partyOffending: many(disputes, {
		relationName: "disputes_partyOffending_party_id"
	}),
	disputes_partyLogging: many(disputes, {
		relationName: "disputes_partyLogging_party_id"
	}),
	leads: many(lead),
	issuingAuthorities: many(issuingAuthority),
	partyType: one(partyType, {
		fields: [party.partyTypeId],
		references: [partyType.id]
	}),
	partyStatus: one(partyStatus, {
		fields: [party.statusId],
		references: [partyStatus.id]
	}),
	partyIdentifications: many(partyIdentification),
	socialProfiles: many(socialProfile),
	verifications: many(verification),
	vehiclePossessions_fromPartyId: many(vehiclePossessions, {
		relationName: "vehiclePossessions_fromPartyId_party_id"
	}),
	vehiclePossessions_toPartyId: many(vehiclePossessions, {
		relationName: "vehiclePossessions_toPartyId_party_id"
	}),
	vehicles: many(vehicles),
	companies: many(company),
	organizations: many(organization),
}));

export const bookingsRelations = relations(bookings, ({one}) => ({
	party: one(party, {
		fields: [bookings.partyId],
		references: [party.id]
	}),
	vehicle: one(vehicles, {
		fields: [bookings.vehicleId],
		references: [vehicles.id]
	}),
}));

export const vehiclesRelations = relations(vehicles, ({one, many}) => ({
	bookings: many(bookings),
	vehicleInspections: many(vehicleInspections),
	vehicleMaintenances: many(vehicleMaintenance),
	vehiclePossessions: many(vehiclePossessions),
	vehicleMedias: many(vehicleMedia),
	vehicleModel: one(vehicleModel, {
		fields: [vehicles.modelId],
		references: [vehicleModel.id]
	}),
	party: one(party, {
		fields: [vehicles.partyId],
		references: [party.id]
	}),
	vehicleDocuments: many(vehicleDocuments),
}));

export const complianceRequirementRelations = relations(complianceRequirement, ({one}) => ({
	complianceSet: one(complianceSet, {
		fields: [complianceRequirement.complianceSetId],
		references: [complianceSet.id]
	}),
	issuingAuthority: one(issuingAuthority, {
		fields: [complianceRequirement.issuingAuthorityId],
		references: [issuingAuthority.id]
	}),
	party: one(party, {
		fields: [complianceRequirement.partyId],
		references: [party.id]
	}),
	complianceRequirementType: one(complianceRequirementType, {
		fields: [complianceRequirement.requirementTypeId],
		references: [complianceRequirementType.id]
	}),
	individual_reviewedById: one(individual, {
		fields: [complianceRequirement.reviewedById],
		references: [individual.id],
		relationName: "complianceRequirement_reviewedById_individual_id"
	}),
	individual_uploadedIndividualId: one(individual, {
		fields: [complianceRequirement.uploadedIndividualId],
		references: [individual.id],
		relationName: "complianceRequirement_uploadedIndividualId_individual_id"
	}),
	verification: one(verification, {
		fields: [complianceRequirement.verificationId],
		references: [verification.id]
	}),
}));

export const complianceSetRelations = relations(complianceSet, ({many}) => ({
	complianceRequirements: many(complianceRequirement),
	complianceSetRequirementTypeMappings: many(complianceSetRequirementTypeMapping),
}));

export const issuingAuthorityRelations = relations(issuingAuthority, ({one, many}) => ({
	complianceRequirements: many(complianceRequirement),
	party: one(party, {
		fields: [issuingAuthority.partyId],
		references: [party.id]
	}),
}));

export const complianceRequirementTypeRelations = relations(complianceRequirementType, ({many}) => ({
	complianceRequirements: many(complianceRequirement),
	complianceSetRequirementTypeMappings: many(complianceSetRequirementTypeMapping),
}));

export const individualRelations = relations(individual, ({one, many}) => ({
	complianceRequirements_reviewedById: many(complianceRequirement, {
		relationName: "complianceRequirement_reviewedById_individual_id"
	}),
	complianceRequirements_uploadedIndividualId: many(complianceRequirement, {
		relationName: "complianceRequirement_uploadedIndividualId_individual_id"
	}),
	party: one(party, {
		fields: [individual.partyId],
		references: [party.id]
	}),
}));

export const verificationRelations = relations(verification, ({one, many}) => ({
	complianceRequirements: many(complianceRequirement),
	party: one(party, {
		fields: [verification.verifyingPartyId],
		references: [party.id]
	}),
}));

export const contactPointRelations = relations(contactPoint, ({one}) => ({
	addressType: one(addressType, {
		fields: [contactPoint.addressTypeId],
		references: [addressType.id]
	}),
	contactPointType: one(contactPointType, {
		fields: [contactPoint.contactPointTypeId],
		references: [contactPointType.id]
	}),
	party: one(party, {
		fields: [contactPoint.partyId],
		references: [party.id]
	}),
}));

export const addressTypeRelations = relations(addressType, ({many}) => ({
	contactPoints: many(contactPoint),
}));

export const contactPointTypeRelations = relations(contactPointType, ({many}) => ({
	contactPoints: many(contactPoint),
}));

export const disputeMediaRelations = relations(disputeMedia, ({one}) => ({
	dispute: one(disputes, {
		fields: [disputeMedia.disputeId],
		references: [disputes.id]
	}),
}));

export const disputesRelations = relations(disputes, ({one, many}) => ({
	disputeMedias: many(disputeMedia),
	party_partyOffending: one(party, {
		fields: [disputes.partyOffending],
		references: [party.id],
		relationName: "disputes_partyOffending_party_id"
	}),
	party_partyLogging: one(party, {
		fields: [disputes.partyLogging],
		references: [party.id],
		relationName: "disputes_partyLogging_party_id"
	}),
	company: one(company, {
		fields: [disputes.companyId],
		references: [company.id]
	}),
	disputeComments: many(disputeComments),
}));

export const leadRelations = relations(lead, ({one}) => ({
	party: one(party, {
		fields: [lead.partyId],
		references: [party.id]
	}),
	leadSource: one(leadSource, {
		fields: [lead.sourceId],
		references: [leadSource.id]
	}),
	leadStatus: one(leadStatus, {
		fields: [lead.statusId],
		references: [leadStatus.id]
	}),
}));

export const leadSourceRelations = relations(leadSource, ({many}) => ({
	leads: many(lead),
}));

export const leadStatusRelations = relations(leadStatus, ({many}) => ({
	leads: many(lead),
}));

export const partyTypeRelations = relations(partyType, ({many}) => ({
	parties: many(party),
}));

export const partyStatusRelations = relations(partyStatus, ({many}) => ({
	parties: many(party),
}));

export const partyIdentificationRelations = relations(partyIdentification, ({one}) => ({
	identificationType: one(identificationType, {
		fields: [partyIdentification.identificationTypeId],
		references: [identificationType.id]
	}),
	party: one(party, {
		fields: [partyIdentification.partyId],
		references: [party.id]
	}),
}));

export const identificationTypeRelations = relations(identificationType, ({many}) => ({
	partyIdentifications: many(partyIdentification),
}));

export const vehicleInspectionsRelations = relations(vehicleInspections, ({one, many}) => ({
	vehiclePossession: one(vehiclePossessions, {
		fields: [vehicleInspections.possessionId],
		references: [vehiclePossessions.id]
	}),
	vehicle: one(vehicles, {
		fields: [vehicleInspections.vehicleId],
		references: [vehicles.id]
	}),
	vehiclePhotos: many(vehiclePhotos),
}));

export const vehiclePossessionsRelations = relations(vehiclePossessions, ({one, many}) => ({
	vehicleInspections: many(vehicleInspections),
	party_fromPartyId: one(party, {
		fields: [vehiclePossessions.fromPartyId],
		references: [party.id],
		relationName: "vehiclePossessions_fromPartyId_party_id"
	}),
	party_toPartyId: one(party, {
		fields: [vehiclePossessions.toPartyId],
		references: [party.id],
		relationName: "vehiclePossessions_toPartyId_party_id"
	}),
	vehicle: one(vehicles, {
		fields: [vehiclePossessions.vehicleId],
		references: [vehicles.id]
	}),
}));

export const vehicleMaintenanceRelations = relations(vehicleMaintenance, ({one}) => ({
	vehicle: one(vehicles, {
		fields: [vehicleMaintenance.vehicleId],
		references: [vehicles.id]
	}),
}));

export const socialProfileRelations = relations(socialProfile, ({one}) => ({
	party: one(party, {
		fields: [socialProfile.partyId],
		references: [party.id]
	}),
	socialMediaType: one(socialMediaType, {
		fields: [socialProfile.socialMediaTypeId],
		references: [socialMediaType.id]
	}),
}));

export const socialMediaTypeRelations = relations(socialMediaType, ({many}) => ({
	socialProfiles: many(socialProfile),
}));

export const vehicleModelMediaRelations = relations(vehicleModelMedia, ({one}) => ({
	vehicleModel: one(vehicleModel, {
		fields: [vehicleModelMedia.vehicleModelId],
		references: [vehicleModel.id]
	}),
}));

export const vehicleModelRelations = relations(vehicleModel, ({one, many}) => ({
	vehicleModelMedias: many(vehicleModelMedia),
	vehicleMake: one(vehicleMake, {
		fields: [vehicleModel.makeId],
		references: [vehicleMake.id]
	}),
	vehicles: many(vehicles),
}));

export const vehiclePhotosRelations = relations(vehiclePhotos, ({one}) => ({
	vehicleInspection: one(vehicleInspections, {
		fields: [vehiclePhotos.inspectionId],
		references: [vehicleInspections.id]
	}),
}));

export const vehicleMediaRelations = relations(vehicleMedia, ({one}) => ({
	vehicle: one(vehicles, {
		fields: [vehicleMedia.vehicleId],
		references: [vehicles.id]
	}),
}));

export const vehicleMakeRelations = relations(vehicleMake, ({many}) => ({
	vehicleModels: many(vehicleModel),
}));

export const citiesRelations = relations(cities, ({many}) => ({
	companies: many(company),
}));

export const disputeCommentsRelations = relations(disputeComments, ({one, many}) => ({
	dispute: one(disputes, {
		fields: [disputeComments.disputeId],
		references: [disputes.id]
	}),
	disputeComment: one(disputeComments, {
		fields: [disputeComments.replyToCommentId],
		references: [disputeComments.id],
		relationName: "disputeComments_replyToCommentId_disputeComments_id"
	}),
	disputeComments: many(disputeComments, {
		relationName: "disputeComments_replyToCommentId_disputeComments_id"
	}),
}));

export const organizationRelations = relations(organization, ({one}) => ({
	party: one(party, {
		fields: [organization.partyId],
		references: [party.id]
	}),
}));

export const vehicleDocumentsRelations = relations(vehicleDocuments, ({one}) => ({
	vehicle: one(vehicles, {
		fields: [vehicleDocuments.vehicleId],
		references: [vehicles.id]
	}),
}));

export const companyOwnershipInviteRelations = relations(companyOwnershipInvite, ({one}) => ({
	company: one(company, {
		fields: [companyOwnershipInvite.companyId],
		references: [company.id]
	}),
}));

export const complianceSetRequirementTypeMappingRelations = relations(complianceSetRequirementTypeMapping, ({one}) => ({
	complianceRequirementType: one(complianceRequirementType, {
		fields: [complianceSetRequirementTypeMapping.complianceRequirementTypeId],
		references: [complianceRequirementType.id]
	}),
	complianceSet: one(complianceSet, {
		fields: [complianceSetRequirementTypeMapping.complianceSetId],
		references: [complianceSet.id]
	}),
}));

export const companyNotificationPreferencesRelations = relations(companyNotificationPreferences, ({one}) => ({
	company: one(company, {
		fields: [companyNotificationPreferences.companyId],
		references: [company.id]
	}),
}));