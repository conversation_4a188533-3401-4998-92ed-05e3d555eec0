"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  DisputeCreate,
  DisputeRead,
  DisputeUpdate,
  DisputeCreateWithMedia,
} from "@/types/disputes";

export async function createDispute(
  dispute: DisputeCreate
): Promise<DisputeRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/disputes", {
    method: "POST",
    body: JSON.stringify(dispute),
  });
  return response.json();
}

export async function getDispute(id: number): Promise<DisputeRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/disputes/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function getAllDisputes(): Promise<DisputeRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/disputes", {
    method: "GET",
  });
  return response.json();
}

export async function updateDispute(
  dispute: DisputeUpdate
): Promise<DisputeRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/disputes/${dispute.id}`, {
    method: "PUT",
    body: JSON.stringify(dispute),
  });
  return response.json();
}

export async function deleteDispute(id: number): Promise<{ message: string }> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/disputes/${id}`, {
    method: "DELETE",
  });
  return response.json();
}

export async function createDisputeWithMedial(
  dispute: DisputeCreateWithMedia
): Promise<DisputeRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/disputes/with-media", {
    method: "POST",
    body: JSON.stringify(dispute),
  });
  return response.json();
}
